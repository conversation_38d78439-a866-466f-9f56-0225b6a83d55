<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>航班搜索步骤导航</title>
    <link href="../output.css" rel="stylesheet" />
  </head>

  <body class="bg-gray-50">
    <div class="w-full bg-white shadow-sm">
      <!-- 航班信息栏 -->
      <div class="px-6 py-3 border-b border-gray-200">
        <div class="flex items-center space-x-4 text-sm text-gray-600">
          <span class="font-medium bg-F8F1E5">One way</span>
          <div class="flex items-center space-x-2">
            <span class="font-semibold text-gray-900">Beijing</span>
            <svg
              class="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              ></path>
            </svg>
            <span class="font-semibold text-gray-900">Guangzhou</span>
          </div>
        </div>
        <div class="mt-1 text-xs text-gray-500">
          Departure: 2025-06-14 | Adults1, Children0, Infants0
        </div>
      </div>

      <!-- 步骤导航栏 -->
      <div class="relative">
        <div class="flex">
          <!-- Edit search 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-600 border-r border-gray-200"
            >
              <span class="text-sm font-medium">Edit search</span>
              <svg
                class="w-4 h-4 ml-2 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </div>
          </div>

          <!-- Select 步骤 (当前激活) -->
          <div class="flex-1 relative">
            <!-- 红色箭头背景 -->
            <div class="absolute inset-0 bg-red-600 clip-path-arrow"></div>
            <div
              class="relative flex items-center justify-center py-4 px-6 text-white"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-red-600 font-bold text-sm">1</span>
                </div>
                <span class="text-sm font-medium">Select</span>
              </div>
            </div>
            <!-- 自定义箭头形状 -->
            <style>
              .clip-path-arrow {
                clip-path: polygon(
                  0 0,
                  calc(100% - 20px) 0,
                  100% 50%,
                  calc(100% - 20px) 100%,
                  0 100%,
                  20px 50%
                );
              }
            </style>
          </div>

          <!-- Review 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-400"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-white font-bold text-sm">2</span>
                </div>
                <span class="text-sm font-medium">Review</span>
              </div>
            </div>
          </div>

          <!-- Information 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-400"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-white font-bold text-sm">3</span>
                </div>
                <span class="text-sm font-medium">Information</span>
              </div>
            </div>
          </div>

          <!-- Self-service 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-400"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-white font-bold text-sm">4</span>
                </div>
                <span class="text-sm font-medium">Self-service</span>
              </div>
            </div>
          </div>

          <!-- Pay 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-400"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-white font-bold text-sm">5</span>
                </div>
                <span class="text-sm font-medium">Pay</span>
              </div>
            </div>
          </div>

          <!-- Completed 步骤 -->
          <div class="flex-1 relative">
            <div
              class="flex items-center justify-center py-4 px-6 bg-gray-100 text-gray-400"
            >
              <div class="flex items-center">
                <div
                  class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-white font-bold text-sm">6</span>
                </div>
                <span class="text-sm font-medium">Completed</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
