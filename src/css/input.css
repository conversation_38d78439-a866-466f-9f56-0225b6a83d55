@import 'tailwindcss';

@theme {
  /* 品牌色 */
  --color-brand-1: #cc0100;
  --color-brand-2: #942531;
  --color-brand-3: #942531;
  --color-brand-4: #e64343;

  /* 辅色 */
  --color-sub-1: #f8f1e5;
  --color-sub-1-30: #4df8f1e5;
  --color-sub-2: #b4aba4;
  --color-sub-3: #7a6a5e;
  --color-sub-4: #4f3d1e;
  --color-sub-5: #ebddba;

  /* 中性色 */
  --color-gray-0: #ffffff;
  --color-gray-1.1: #f4f6f9;
  --color-gray-1: #dbd7d4;
  --color-gray-2: #cccccc;
  --color-gray-3: #554e49;
  --color-gray-4: #2c2420;
  --color-gray-5: #101010;
  --color-gray-6: #4c5560;

  /* 状态色 */
  --color-green-1: #edf9e8;
  --color-green-2: #52c41a;
  --color-red-1: #ffeded;
  --color-red-2: #ff4d4f;
  --color-orange-1: #fff3ed;
  --color-orange-2: #ff8e4d;
  --color-orange-3: #c47330;
  --color-orange-linear: linear-gradient(270deg, #c47330 0%, #e18132 100%);
  --color-yellow-1: #fdf7e8;
  --color-yellow-2: #eeb71c;

  --text-10: 10px;
  --text-12: 12px;
  --text-14: 14px;
  --text-16: 16px;
  --text-20: 20px;
  --text-24: 24px;
  --text-28: 28px;
  --text-30: 30px;
  --text-40: 40px;

  --radius-4: 4px;
  --radius-8: 8px;

  --spacing: 1px;
}
