"use strict";var xi=Object.defineProperty;var Ai=(t,r)=>{for(var i in r)xi(t,i,{get:r[i],enumerable:!0})};var vt={};Ai(vt,{Features:()=>Se,Polyfills:()=>tt,__unstable__loadDesignSystem:()=>uo,compile:()=>so,compileAst:()=>yi,default:()=>We});var Mt="4.1.11";var Pe=92,Be=47,qe=42,Wt=34,Bt=39,Ni=58,Ge=59,ne=10,Ye=13,Oe=32,He=9,qt=123,kt=125,xt=40,Ht=41,$i=91,Vi=93,Gt=45,bt=64,Si=33;function ve(t,r){let i=r?.from?{file:r.from,code:t}:null;t[0]==="\uFEFF"&&(t=" "+t.slice(1));let e=[],n=[],s=[],a=null,f=null,u="",c="",d=0,m;for(let g=0;g<t.length;g++){let w=t.charCodeAt(g);if(!(w===Ye&&(m=t.charCodeAt(g+1),m===ne)))if(w===Pe)u===""&&(d=g),u+=t.slice(g,g+2),g+=1;else if(w===Be&&t.charCodeAt(g+1)===qe){let v=g;for(let y=g+2;y<t.length;y++)if(m=t.charCodeAt(y),m===Pe)y+=1;else if(m===qe&&t.charCodeAt(y+1)===Be){g=y+1;break}let x=t.slice(v,g+1);if(x.charCodeAt(2)===Si){let y=Ze(x.slice(2,-2));n.push(y),i&&(y.src=[i,v,g+1],y.dst=[i,v,g+1])}}else if(w===Bt||w===Wt){let v=Yt(t,g,w);u+=t.slice(g,v+1),g=v}else{if((w===Oe||w===ne||w===He)&&(m=t.charCodeAt(g+1))&&(m===Oe||m===ne||m===He||m===Ye&&(m=t.charCodeAt(g+2))&&m==ne))continue;if(w===ne){if(u.length===0)continue;m=u.charCodeAt(u.length-1),m!==Oe&&m!==ne&&m!==He&&(u+=" ")}else if(w===Gt&&t.charCodeAt(g+1)===Gt&&u.length===0){let v="",x=g,y=-1;for(let b=g+2;b<t.length;b++)if(m=t.charCodeAt(b),m===Pe)b+=1;else if(m===Bt||m===Wt)b=Yt(t,b,m);else if(m===Be&&t.charCodeAt(b+1)===qe){for(let S=b+2;S<t.length;S++)if(m=t.charCodeAt(S),m===Pe)S+=1;else if(m===qe&&t.charCodeAt(S+1)===Be){b=S+1;break}}else if(y===-1&&m===Ni)y=u.length+b-x;else if(m===Ge&&v.length===0){u+=t.slice(x,b),g=b;break}else if(m===xt)v+=")";else if(m===$i)v+="]";else if(m===qt)v+="}";else if((m===kt||t.length-1===b)&&v.length===0){g=b-1,u+=t.slice(x,b);break}else(m===Ht||m===Vi||m===kt)&&v.length>0&&t[b]===v[v.length-1]&&(v=v.slice(0,-1));let V=yt(u,y);if(!V)throw new Error("Invalid custom property, expected a value");i&&(V.src=[i,x,g],V.dst=[i,x,g]),a?a.nodes.push(V):e.push(V),u=""}else if(w===Ge&&u.charCodeAt(0)===bt)f=_e(u),i&&(f.src=[i,d,g],f.dst=[i,d,g]),a?a.nodes.push(f):e.push(f),u="",f=null;else if(w===Ge&&c[c.length-1]!==")"){let v=yt(u);if(!v)throw u.length===0?new Error("Unexpected semicolon"):new Error(`Invalid declaration: \`${u.trim()}\``);i&&(v.src=[i,d,g],v.dst=[i,d,g]),a?a.nodes.push(v):e.push(v),u=""}else if(w===qt&&c[c.length-1]!==")")c+="}",f=G(u.trim()),i&&(f.src=[i,d,g],f.dst=[i,d,g]),a&&a.nodes.push(f),s.push(a),a=f,u="",f=null;else if(w===kt&&c[c.length-1]!==")"){if(c==="")throw new Error("Missing opening {");if(c=c.slice(0,-1),u.length>0)if(u.charCodeAt(0)===bt)f=_e(u),i&&(f.src=[i,d,g],f.dst=[i,d,g]),a?a.nodes.push(f):e.push(f),u="",f=null;else{let x=u.indexOf(":");if(a){let y=yt(u,x);if(!y)throw new Error(`Invalid declaration: \`${u.trim()}\``);i&&(y.src=[i,d,g],y.dst=[i,d,g]),a.nodes.push(y)}}let v=s.pop()??null;v===null&&a&&e.push(a),a=v,u="",f=null}else if(w===xt)c+=")",u+="(";else if(w===Ht){if(c[c.length-1]!==")")throw new Error("Missing opening (");c=c.slice(0,-1),u+=")"}else{if(u.length===0&&(w===Oe||w===ne||w===He))continue;u===""&&(d=g),u+=String.fromCharCode(w)}}}if(u.charCodeAt(0)===bt){let g=_e(u);i&&(g.src=[i,d,t.length],g.dst=[i,d,t.length]),e.push(g)}if(c.length>0&&a){if(a.kind==="rule")throw new Error(`Missing closing } at ${a.selector}`);if(a.kind==="at-rule")throw new Error(`Missing closing } at ${a.name} ${a.params}`)}return n.length>0?n.concat(e):e}function _e(t,r=[]){let i=t,e="";for(let n=5;n<t.length;n++){let s=t.charCodeAt(n);if(s===Oe||s===xt){i=t.slice(0,n),e=t.slice(n);break}}return F(i.trim(),e.trim(),r)}function yt(t,r=t.indexOf(":")){if(r===-1)return null;let i=t.indexOf("!important",r+1);return l(t.slice(0,r).trim(),t.slice(r+1,i===-1?t.length:i).trim(),i!==-1)}function Yt(t,r,i){let e;for(let n=r+1;n<t.length;n++)if(e=t.charCodeAt(n),e===Pe)n+=1;else{if(e===i)return n;if(e===Ge&&(t.charCodeAt(n+1)===ne||t.charCodeAt(n+1)===Ye&&t.charCodeAt(n+2)===ne))throw new Error(`Unterminated string: ${t.slice(r,n+1)+String.fromCharCode(i)}`);if(e===ne||e===Ye&&t.charCodeAt(n+1)===ne)throw new Error(`Unterminated string: ${t.slice(r,n)+String.fromCharCode(i)}`)}return r}function de(t){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let r=String(t),i=r.length,e=-1,n,s="",a=r.charCodeAt(0);if(i===1&&a===45)return"\\"+r;for(;++e<i;){if(n=r.charCodeAt(e),n===0){s+="\uFFFD";continue}if(n>=1&&n<=31||n===127||e===0&&n>=48&&n<=57||e===1&&n>=48&&n<=57&&a===45){s+="\\"+n.toString(16)+" ";continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){s+=r.charAt(e);continue}s+="\\"+r.charAt(e)}return s}function we(t){return t.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,r=>r.length>2?String.fromCodePoint(Number.parseInt(r.slice(1).trim(),16)):r[1])}var Jt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function Zt(t,r){return(Jt.get(r)??[]).some(i=>t===i||t.startsWith(`${i}-`))}var Je=class{constructor(r=new Map,i=new Set([])){this.values=r;this.keyframes=i}prefix=null;get size(){return this.values.size}add(r,i,e=0,n){if(r.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${r}\``);r==="--*"?this.values.clear():this.clearNamespace(r.slice(0,-2),0)}if(e&4){let s=this.values.get(r);if(s&&!(s.options&4))return}i==="initial"?this.values.delete(r):this.values.set(r,{value:i,options:e,src:n})}keysInNamespaces(r){let i=[];for(let e of r){let n=`${e}-`;for(let s of this.values.keys())s.startsWith(n)&&s.indexOf("--",2)===-1&&(Zt(s,e)||i.push(s.slice(n.length)))}return i}get(r){for(let i of r){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(r){return(this.getOptions(r)&4)===4}getOptions(r){return r=we(this.#r(r)),this.values.get(r)?.options??0}entries(){return this.prefix?Array.from(this.values,r=>(r[0]=this.prefixKey(r[0]),r)):this.values.entries()}prefixKey(r){return this.prefix?`--${this.prefix}-${r.slice(2)}`:r}#r(r){return this.prefix?`--${r.slice(3+this.prefix.length)}`:r}clearNamespace(r,i){let e=Jt.get(r)??[];e:for(let n of this.values.keys())if(n.startsWith(r)){if(i!==0&&(this.getOptions(n)&i)!==i)continue;for(let s of e)if(n.startsWith(s))continue e;this.values.delete(n)}}#e(r,i){for(let e of i){let n=r!==null?`${e}-${r}`:e;if(!this.values.has(n))if(r!==null&&r.includes(".")){if(n=`${e}-${r.replaceAll(".","_")}`,!this.values.has(n))continue}else continue;if(!Zt(n,e))return n}return null}#t(r){let i=this.values.get(r);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${de(this.prefixKey(r))}${e?`, ${e}`:""})`}markUsedVariable(r){let i=we(this.#r(r)),e=this.values.get(i);if(!e)return!1;let n=e.options&16;return e.options|=16,!n}resolve(r,i,e=0){let n=this.#e(r,i);if(!n)return null;let s=this.values.get(n);return(e|s.options)&1?s.value:this.#t(n)}resolveValue(r,i){let e=this.#e(r,i);return e?this.values.get(e).value:null}resolveWith(r,i,e=[]){let n=this.#e(r,i);if(!n)return null;let s={};for(let f of e){let u=`${n}${f}`,c=this.values.get(u);c&&(c.options&1?s[f]=c.value:s[f]=this.#t(u))}let a=this.values.get(n);return a.options&1?[a.value,s]:[this.#t(n),s]}namespace(r){let i=new Map,e=`${r}-`;for(let[n,s]of this.values)n===r?i.set(null,s.value):n.startsWith(`${e}-`)?i.set(n.slice(r.length),s.value):n.startsWith(e)&&i.set(n.slice(e.length),s.value);return i}addKeyframes(r){this.keyframes.add(r)}getKeyframes(){return Array.from(this.keyframes)}};var B=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function Ct(t){return{kind:"word",value:t}}function Ei(t,r){return{kind:"function",value:t,nodes:r}}function Ti(t){return{kind:"separator",value:t}}function ee(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,f=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):t[e]=u)}})??0;if(s){f===0?e--:e+=a-1;continue}if(f===2)return 2;if(f!==1&&n.kind==="function"&&ee(n.nodes,r,n)===2)return 2}}function Z(t){let r="";for(let i of t)switch(i.kind){case"word":case"separator":{r+=i.value;break}case"function":r+=i.value+"("+Z(i.nodes)+")"}return r}var Qt=92,Ri=41,Xt=58,er=44,Pi=34,tr=61,rr=62,ir=60,nr=10,Oi=40,_i=39,or=47,lr=32,ar=9;function q(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let f=t.charCodeAt(a);switch(f){case Qt:{n+=t[a]+t[a+1],a++;break}case Xt:case er:case tr:case rr:case ir:case nr:case or:case lr:case ar:{if(n.length>0){let m=Ct(n);e?e.nodes.push(m):r.push(m),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Xt&&s!==er&&s!==tr&&s!==rr&&s!==ir&&s!==nr&&s!==or&&s!==lr&&s!==ar));c++);a=c-1;let d=Ti(t.slice(u,c));e?e.nodes.push(d):r.push(d);break}case _i:case Pi:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Qt)c+=1;else if(s===f){a=c;break}n+=t.slice(u,a+1);break}case Oi:{let u=Ei(n,[]);n="",e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case Ri:{let u=i.pop();if(n.length>0){let c=Ct(n);u?.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}default:n+=String.fromCharCode(f)}}return n.length>0&&r.push(Ct(n)),r}function Qe(t){let r=[];return ee(q(t),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||r.push(e.value)}),1}),r}var Ki=64;function M(t,r=[]){return{kind:"rule",selector:t,nodes:r}}function F(t,r="",i=[]){return{kind:"at-rule",name:t,params:r,nodes:i}}function G(t,r=[]){return t.charCodeAt(0)===Ki?_e(t,r):M(t,r)}function l(t,r,i=!1){return{kind:"declaration",property:t,value:r,important:i}}function Ze(t){return{kind:"comment",value:t}}function se(t,r){return{kind:"context",context:t,nodes:r}}function z(t){return{kind:"at-root",nodes:t}}function I(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="context"){if(I(s.nodes,r,i,{...e,...s.context})===2)return 2;continue}i.push(s);let f=!1,u=0,c=r(s,{parent:a,context:e,path:i,replaceWith(d){f||(f=!0,Array.isArray(d)?d.length===0?(t.splice(n,1),u=0):d.length===1?(t[n]=d[0],u=1):(t.splice(n,1,...d),u=d.length):(t[n]=d,u=1))}})??0;if(i.pop(),f){c===0?n--:n+=u-1;continue}if(c===2)return 2;if(c!==1&&"nodes"in s){i.push(s);let d=I(s.nodes,r,i,e);if(i.pop(),d===2)return 2}}}function Xe(t,r,i=[],e={}){for(let n=0;n<t.length;n++){let s=t[n],a=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Xe(s.nodes,r,i,e),i.pop();else if(s.kind==="context"){Xe(s.nodes,r,i,{...e,...s.context});continue}i.push(s),r(s,{parent:a,context:e,path:i,replaceWith(f){Array.isArray(f)?f.length===0?t.splice(n,1):f.length===1?t[n]=f[0]:t.splice(n,1,...f):t[n]=f,n+=f.length-1}}),i.pop()}}function be(t,r,i=3){let e=[],n=new Set,s=new B(()=>new Set),a=new B(()=>new Set),f=new Set,u=new Set,c=[],d=[],m=new B(()=>new Set);function g(v,x,y={},V=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(y.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}y.keyframes||s.get(x).add(v)}if(v.value.includes("var("))if(y.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let b of Qe(v.value))m.get(b).add(v.property);else r.trackUsedVariables(v.value);if(v.property==="animation")for(let b of sr(v.value))u.add(b);i&2&&v.value.includes("color-mix(")&&a.get(x).add(v),x.push(v)}else if(v.kind==="rule")if(v.selector==="&")for(let b of v.nodes){let S=[];g(b,S,y,V+1),S.length>0&&x.push(...S)}else{let b={...v,nodes:[]};for(let S of v.nodes)g(S,b.nodes,y,V+1);b.nodes.length>0&&x.push(b)}else if(v.kind==="at-rule"&&v.name==="@property"&&V===0){if(n.has(v.params))return;if(i&1){let S=v.params,R=null,L=!1;for(let K of v.nodes)K.kind==="declaration"&&(K.property==="initial-value"?R=K.value:K.property==="inherits"&&(L=K.value==="true"));let P=l(S,R??"initial");P.src=v.src,L?c.push(P):d.push(P)}n.add(v.params);let b={...v,nodes:[]};for(let S of v.nodes)g(S,b.nodes,y,V+1);x.push(b)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(y={...y,keyframes:!0});let b={...v,nodes:[]};for(let S of v.nodes)g(S,b.nodes,y,V+1);v.name==="@keyframes"&&y.theme&&f.add(b),(b.nodes.length>0||b.name==="@layer"||b.name==="@charset"||b.name==="@custom-media"||b.name==="@namespace"||b.name==="@import")&&x.push(b)}else if(v.kind==="at-root")for(let b of v.nodes){let S=[];g(b,S,y,0);for(let R of S)e.push(R)}else if(v.kind==="context"){if(v.context.reference)return;for(let b of v.nodes)g(b,x,{...y,...v.context},V)}else v.kind==="comment"&&x.push(v)}let w=[];for(let v of t)g(v,w,{},0);e:for(let[v,x]of s)for(let y of x){if(ur(y.property,r.theme,m)){if(y.property.startsWith(r.theme.prefixKey("--animate-")))for(let S of sr(y.value))u.add(S);continue}let b=v.indexOf(y);if(v.splice(b,1),v.length===0){let S=Ui(w,R=>R.kind==="rule"&&R.nodes===v);if(!S||S.length===0)continue e;S.unshift({kind:"at-root",nodes:w});do{let R=S.pop();if(!R)break;let L=S[S.length-1];if(!L||L.kind!=="at-root"&&L.kind!=="at-rule")break;let P=L.nodes.indexOf(R);if(P===-1)break;L.nodes.splice(P,1)}while(!0);continue e}}for(let v of f)if(!u.has(v.params)){let x=e.indexOf(v);e.splice(x,1)}if(w=w.concat(e),i&2)for(let[v,x]of a)for(let y of x){let V=v.indexOf(y);if(V===-1||y.value==null)continue;let b=q(y.value),S=!1;if(ee(b,(P,{replaceWith:K})=>{if(P.kind!=="function"||P.value!=="color-mix")return;let _=!1,H=!1;if(ee(P.nodes,(j,{replaceWith:W})=>{if(j.kind=="word"&&j.value.toLowerCase()==="currentcolor"){H=!0,S=!0;return}let J=j,ie=null,o=new Set;do{if(J.kind!=="function"||J.value!=="var")return;let p=J.nodes[0];if(!p||p.kind!=="word")return;let h=p.value;if(o.has(h)){_=!0;return}if(o.add(h),S=!0,ie=r.theme.resolveValue(null,[p.value]),!ie){_=!0;return}if(ie.toLowerCase()==="currentcolor"){H=!0;return}ie.startsWith("var(")?J=q(ie)[0]:J=null}while(J);W({kind:"word",value:ie})}),_||H){let j=P.nodes.findIndex(J=>J.kind==="separator"&&J.value.trim().includes(","));if(j===-1)return;let W=P.nodes.length>j?P.nodes[j+1]:null;if(!W)return;K(W)}else if(S){let j=P.nodes[2];j.kind==="word"&&(j.value==="oklab"||j.value==="oklch"||j.value==="lab"||j.value==="lch")&&(j.value="srgb")}}),!S)continue;let R={...y,value:Z(b)},L=G("@supports (color: color-mix(in lab, red, red))",[y]);L.src=y.src,v.splice(V,1,R,L)}if(i&1){let v=[];if(c.length>0){let x=G(":root, :host",c);x.src=c[0].src,v.push(x)}if(d.length>0){let x=G("*, ::before, ::after, ::backdrop",d);x.src=d[0].src,v.push(x)}if(v.length>0){let x=w.findIndex(b=>!(b.kind==="comment"||b.kind==="at-rule"&&(b.name==="@charset"||b.name==="@import"))),y=F("@layer","properties",[]);y.src=v[0].src,w.splice(x<0?w.length:x,0,y);let V=G("@layer properties",[F("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]);V.src=v[0].src,V.nodes[0].src=v[0].src,w.push(V)}}return w}function oe(t,r){let i=0,e={file:null,code:""};function n(a,f=0){let u="",c="  ".repeat(f);if(a.kind==="declaration"){if(u+=`${c}${a.property}: ${a.value}${a.important?" !important":""};
`,r){i+=c.length;let d=i;i+=a.property.length,i+=2,i+=a.value?.length??0,a.important&&(i+=11);let m=i;i+=2,a.dst=[e,d,m]}}else if(a.kind==="rule"){if(u+=`${c}${a.selector} {
`,r){i+=c.length;let d=i;i+=a.selector.length,i+=1;let m=i;a.dst=[e,d,m],i+=2}for(let d of a.nodes)u+=n(d,f+1);u+=`${c}}
`,r&&(i+=c.length,i+=2)}else if(a.kind==="at-rule"){if(a.nodes.length===0){let d=`${c}${a.name} ${a.params};
`;if(r){i+=c.length;let m=i;i+=a.name.length,i+=1,i+=a.params.length;let g=i;i+=2,a.dst=[e,m,g]}return d}if(u+=`${c}${a.name}${a.params?` ${a.params} `:" "}{
`,r){i+=c.length;let d=i;i+=a.name.length,a.params&&(i+=1,i+=a.params.length),i+=1;let m=i;a.dst=[e,d,m],i+=2}for(let d of a.nodes)u+=n(d,f+1);u+=`${c}}
`,r&&(i+=c.length,i+=2)}else if(a.kind==="comment"){if(u+=`${c}/*${a.value}*/
`,r){i+=c.length;let d=i;i+=2+a.value.length+2;let m=i;a.dst=[e,d,m],i+=1}}else if(a.kind==="context"||a.kind==="at-root")return"";return u}let s="";for(let a of t)s+=n(a,0);return e.code=s,s}function Ui(t,r){let i=[];return I(t,(e,{path:n})=>{if(r(e))return i=[...n],2}),i}function ur(t,r,i,e=new Set){if(e.has(t)||(e.add(t),r.getOptions(t)&24))return!0;{let s=i.get(t)??[];for(let a of s)if(ur(a,r,i,e))return!0}return!1}function sr(t){return t.split(/[\s,]+/)}var Nt=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"];function De(t){return t.indexOf("(")!==-1&&Nt.some(r=>t.includes(`${r}(`))}function cr(t){if(!Nt.some(s=>t.includes(s)))return t;let r="",i=[],e=null,n=null;for(let s=0;s<t.length;s++){let a=t.charCodeAt(s);if(a>=48&&a<=57||e!==null&&(a===37||a>=97&&a<=122||a>=65&&a<=90)?e=s:(n=e,e=null),a===40){r+=t[s];let f=s;for(let c=s-1;c>=0;c--){let d=t.charCodeAt(c);if(d>=48&&d<=57)f=c;else if(d>=97&&d<=122)f=c;else break}let u=t.slice(f,s);if(Nt.includes(u)){i.unshift(!0);continue}else if(i[0]&&u===""){i.unshift(!0);continue}i.unshift(!1);continue}else if(a===41)r+=t[s],i.shift();else if(a===44&&i[0]){r+=", ";continue}else{if(a===32&&i[0]&&r.charCodeAt(r.length-1)===32)continue;if((a===43||a===42||a===47||a===45)&&i[0]){let f=r.trimEnd(),u=f.charCodeAt(f.length-1),c=f.charCodeAt(f.length-2),d=t.charCodeAt(s+1);if((u===101||u===69)&&c>=48&&c<=57){r+=t[s];continue}else if(u===43||u===42||u===47||u===45){r+=t[s];continue}else if(u===40||u===44){r+=t[s];continue}else t.charCodeAt(s-1)===32?r+=`${t[s]} `:u>=48&&u<=57||d>=48&&d<=57||u===41||d===40||d===43||d===42||d===47||d===45||n!==null&&n===s-1?r+=` ${t[s]} `:r+=t[s]}else r+=t[s]}}return r}function me(t){if(t.indexOf("(")===-1)return Ne(t);let r=q(t);return $t(r),t=Z(r),t=cr(t),t}function Ne(t,r=!1){let i="";for(let e=0;e<t.length;e++){let n=t[e];n==="\\"&&t[e+1]==="_"?(i+="_",e+=1):n==="_"&&!r?i+=" ":i+=n}return i}function $t(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ne(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ne(r.value);for(let i=0;i<r.nodes.length;i++){if(i==0&&r.nodes[i].kind==="word"){r.nodes[i].value=Ne(r.nodes[i].value,!0);continue}$t([r.nodes[i]])}break}r.value=Ne(r.value),$t(r.nodes);break}case"separator":case"word":{r.value=Ne(r.value);break}default:Li(r)}}function Li(t){throw new Error(`Unexpected value: ${t}`)}var Vt=new Uint8Array(256);function ce(t){let r=0,i=t.length;for(let e=0;e<i;e++){let n=t.charCodeAt(e);switch(n){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=t.charCodeAt(e);if(s===92){e+=1;continue}if(s===n)break}break;case 40:Vt[r]=41,r++;break;case 91:Vt[r]=93,r++;break;case 123:break;case 93:case 125:case 41:if(r===0)return!1;r>0&&n===Vt[r-1]&&r--;break;case 59:if(r===0)return!1;break}}return!0}var rt=new Uint8Array(256);function D(t,r){let i=0,e=[],n=0,s=t.length,a=r.charCodeAt(0);for(let f=0;f<s;f++){let u=t.charCodeAt(f);if(i===0&&u===a){e.push(t.slice(n,f)),n=f+1;continue}switch(u){case 92:f+=1;break;case 39:case 34:for(;++f<s;){let c=t.charCodeAt(f);if(c===92){f+=1;continue}if(c===u)break}break;case 40:rt[i]=41,i++;break;case 91:rt[i]=93,i++;break;case 123:rt[i]=125,i++;break;case 93:case 125:case 41:i>0&&u===rt[i-1]&&i--;break}}return e.push(t.slice(n)),e}var ji=58,fr=45,pr=97,dr=122;function*mr(t,r){let i=D(t,":");if(r.theme.prefix){if(i.length===1||i[0]!==r.theme.prefix)return null;i.shift()}let e=i.pop(),n=[];for(let m=i.length-1;m>=0;--m){let g=r.parseVariant(i[m]);if(g===null)return;n.push(g)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),r.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:n,important:s,raw:t});let[a,f=null,u]=D(e,"/");if(u)return;let c=f===null?null:St(f);if(f!==null&&c===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let m=a.charCodeAt(1);if(m!==fr&&!(m>=pr&&m<=dr))return;a=a.slice(1,-1);let g=a.indexOf(":");if(g===-1||g===0||g===a.length-1)return;let w=a.slice(0,g),v=me(a.slice(g+1));if(!ce(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:c,variants:n,important:s,raw:t};return}let d;if(a[a.length-1]==="]"){let m=a.indexOf("-[");if(m===-1)return;let g=a.slice(0,m);if(!r.utilities.has(g,"functional"))return;let w=a.slice(m+1);d=[[g,w]]}else if(a[a.length-1]===")"){let m=a.indexOf("-(");if(m===-1)return;let g=a.slice(0,m);if(!r.utilities.has(g,"functional"))return;let w=a.slice(m+2,-1),v=D(w,":"),x=null;if(v.length===2&&(x=v[0],w=v[1]),w[0]!=="-"||w[1]!=="-"||!ce(w))return;d=[[g,x===null?`[var(${w})]`:`[${x}:var(${w})]`]]}else d=hr(a,m=>r.utilities.has(m,"functional"));for(let[m,g]of d){let w={kind:"functional",root:m,modifier:c,value:null,variants:n,important:s,raw:t};if(g===null){yield w;continue}{let v=g.indexOf("[");if(v!==-1){if(g[g.length-1]!=="]")return;let y=me(g.slice(v+1,-1));if(!ce(y))continue;let V="";for(let b=0;b<y.length;b++){let S=y.charCodeAt(b);if(S===ji){V=y.slice(0,b),y=y.slice(b+1);break}if(!(S===fr||S>=pr&&S<=dr))break}if(y.length===0||y.trim().length===0)continue;w.value={kind:"arbitrary",dataType:V||null,value:y}}else{let y=f===null||w.modifier?.kind==="arbitrary"?null:`${g}/${f}`;w.value={kind:"named",value:g,fraction:y}}}yield w}}function St(t){if(t[0]==="["&&t[t.length-1]==="]"){let r=me(t.slice(1,-1));return!ce(r)||r.length===0||r.trim().length===0?null:{kind:"arbitrary",value:r}}return t[0]==="("&&t[t.length-1]===")"?(t=t.slice(1,-1),t[0]!=="-"||t[1]!=="-"||!ce(t)?null:(t=`var(${t})`,{kind:"arbitrary",value:me(t)})):{kind:"named",value:t}}function gr(t,r){if(t[0]==="["&&t[t.length-1]==="]"){if(t[1]==="@"&&t.includes("&"))return null;let i=me(t.slice(1,-1));if(!ce(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,n]=D(t,"/");if(n)return null;let s=hr(i,a=>r.variants.has(a));for(let[a,f]of s)switch(r.variants.kind(a)){case"static":return f!==null||e!==null?null:{kind:"static",root:a};case"functional":{let u=e===null?null:St(e);if(e!==null&&u===null)return null;if(f===null)return{kind:"functional",root:a,modifier:u,value:null};if(f[f.length-1]==="]"){if(f[0]!=="[")continue;let c=me(f.slice(1,-1));return!ce(c)||c.length===0||c.trim().length===0?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:c}}}if(f[f.length-1]===")"){if(f[0]!=="(")continue;let c=me(f.slice(1,-1));return!ce(c)||c.length===0||c.trim().length===0||c[0]!=="-"||c[1]!=="-"?null:{kind:"functional",root:a,modifier:u,value:{kind:"arbitrary",value:`var(${c})`}}}return{kind:"functional",root:a,modifier:u,value:{kind:"named",value:f}}}case"compound":{if(f===null)return null;let u=r.parseVariant(f);if(u===null||!r.variants.compoundsWith(a,u))return null;let c=e===null?null:St(e);return e!==null&&c===null?null:{kind:"compound",root:a,modifier:c,variant:u}}}}return null}function*hr(t,r){r(t)&&(yield[t,null]);let i=t.lastIndexOf("-");for(;i>0;){let e=t.slice(0,i);if(r(e)){let n=[e,t.slice(i+1)];if(n[1]==="")break;yield n}i=t.lastIndexOf("-",i-1)}t[0]==="@"&&r("@")&&(yield["@",t.slice(1)])}function vr(t,r){let i=[];for(let n of r.variants)i.unshift(it(n));t.theme.prefix&&i.unshift(t.theme.prefix);let e="";if(r.kind==="static"&&(e+=r.root),r.kind==="functional"&&(e+=r.root,r.value))if(r.value.kind==="arbitrary"){if(r.value!==null){let n=Tt(r.value.value),s=n?r.value.value.slice(4,-1):r.value.value,[a,f]=n?["(",")"]:["[","]"];r.value.dataType?e+=`-${a}${r.value.dataType}:${$e(s)}${f}`:e+=`-${a}${$e(s)}${f}`}}else r.value.kind==="named"&&(e+=`-${r.value.value}`);return r.kind==="arbitrary"&&(e+=`[${r.property}:${$e(r.value)}]`),(r.kind==="arbitrary"||r.kind==="functional")&&(e+=wr(r.modifier)),r.important&&(e+="!"),i.push(e),i.join(":")}function wr(t){if(t===null)return"";let r=Tt(t.value),i=r?t.value.slice(4,-1):t.value,[e,n]=r?["(",")"]:["[","]"];return t.kind==="arbitrary"?`/${e}${$e(i)}${n}`:t.kind==="named"?`/${t.value}`:""}function it(t){if(t.kind==="static")return t.root;if(t.kind==="arbitrary")return`[${$e(Fi(t.selector))}]`;let r="";if(t.kind==="functional"){r+=t.root;let i=t.root!=="@";if(t.value)if(t.value.kind==="arbitrary"){let e=Tt(t.value.value),n=e?t.value.value.slice(4,-1):t.value.value,[s,a]=e?["(",")"]:["[","]"];r+=`${i?"-":""}${s}${$e(n)}${a}`}else t.value.kind==="named"&&(r+=`${i?"-":""}${t.value.value}`)}return t.kind==="compound"&&(r+=t.root,r+="-",r+=it(t.variant)),(t.kind==="functional"||t.kind==="compound")&&(r+=wr(t.modifier)),r}var Ii=new B(t=>{let r=q(t),i=new Set;return ee(r,(e,{parent:n})=>{let s=n===null?r:n.nodes??[];if(e.kind==="word"&&(e.value==="+"||e.value==="-"||e.value==="*"||e.value==="/")){let a=s.indexOf(e)??-1;if(a===-1)return;let f=s[a-1];if(f?.kind!=="separator"||f.value!==" ")return;let u=s[a+1];if(u?.kind!=="separator"||u.value!==" ")return;i.add(f),i.add(u)}else e.kind==="separator"&&e.value.trim()==="/"?e.value="/":e.kind==="separator"&&e.value.length>0&&e.value.trim()===""?(s[0]===e||s[s.length-1]===e)&&i.add(e):e.kind==="separator"&&e.value.trim()===","&&(e.value=",")}),i.size>0&&ee(r,(e,{replaceWith:n})=>{i.has(e)&&(i.delete(e),n([]))}),Et(r),Z(r)});function $e(t){return Ii.get(t)}var zi=new B(t=>{let r=q(t);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?Z(r[2].nodes):t});function Fi(t){return zi.get(t)}function Et(t){for(let r of t)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=Ke(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=Ke(r.value);for(let i=0;i<r.nodes.length;i++)Et([r.nodes[i]]);break}r.value=Ke(r.value),Et(r.nodes);break}case"separator":r.value=Ke(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=Ke(r.value));break}default:Wi(r)}}var Mi=new B(t=>{let r=q(t);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function Tt(t){return Mi.get(t)}function Wi(t){throw new Error(`Unexpected value: ${t}`)}function Ke(t){return t.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}function ye(t,r,i){if(t===r)return 0;let e=t.indexOf("("),n=r.indexOf("("),s=e===-1?t.replace(/[\d.]+/g,""):t.slice(0,e),a=n===-1?r.replace(/[\d.]+/g,""):r.slice(0,n),f=(s===a?0:s<a?-1:1)||(i==="asc"?parseInt(t)-parseInt(r):parseInt(r)-parseInt(t));return Number.isNaN(f)?t<r?-1:1:f}var Bi=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),qi=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i;function kr(t){return t.charCodeAt(0)===35||qi.test(t)||Bi.has(t.toLowerCase())}var Hi={color:kr,length:nt,percentage:Rt,ratio:an,number:yr,integer:T,url:br,position:cn,"bg-size":fn,"line-width":Yi,image:Qi,"family-name":en,"generic-name":Xi,"absolute-size":tn,"relative-size":rn,angle:mn,vector:hn};function Y(t,r){if(t.startsWith("var("))return null;for(let i of r)if(Hi[i]?.(t))return i;return null}var Gi=/^url\(.*\)$/;function br(t){return Gi.test(t)}function Yi(t){return D(t," ").every(r=>nt(r)||yr(r)||r==="thin"||r==="medium"||r==="thick")}var Zi=/^(?:element|image|cross-fade|image-set)\(/,Ji=/^(repeating-)?(conic|linear|radial)-gradient\(/;function Qi(t){let r=0;for(let i of D(t,","))if(!i.startsWith("var(")){if(br(i)){r+=1;continue}if(Ji.test(i)){r+=1;continue}if(Zi.test(i)){r+=1;continue}return!1}return r>0}function Xi(t){return t==="serif"||t==="sans-serif"||t==="monospace"||t==="cursive"||t==="fantasy"||t==="system-ui"||t==="ui-serif"||t==="ui-sans-serif"||t==="ui-monospace"||t==="ui-rounded"||t==="math"||t==="emoji"||t==="fangsong"}function en(t){let r=0;for(let i of D(t,",")){let e=i.charCodeAt(0);if(e>=48&&e<=57)return!1;i.startsWith("var(")||(r+=1)}return r>0}function tn(t){return t==="xx-small"||t==="x-small"||t==="small"||t==="medium"||t==="large"||t==="x-large"||t==="xx-large"||t==="xxx-large"}function rn(t){return t==="larger"||t==="smaller"}var fe=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,nn=new RegExp(`^${fe.source}$`);function yr(t){return nn.test(t)||De(t)}var on=new RegExp(`^${fe.source}%$`);function Rt(t){return on.test(t)||De(t)}var ln=new RegExp(`^${fe.source}s*/s*${fe.source}$`);function an(t){return ln.test(t)||De(t)}var sn=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],un=new RegExp(`^${fe.source}(${sn.join("|")})$`);function nt(t){return un.test(t)||De(t)}function cn(t){let r=0;for(let i of D(t," ")){if(i==="center"||i==="top"||i==="right"||i==="bottom"||i==="left"){r+=1;continue}if(!i.startsWith("var(")){if(nt(i)||Rt(i)){r+=1;continue}return!1}}return r>0}function fn(t){let r=0;for(let i of D(t,",")){if(i==="cover"||i==="contain"){r+=1;continue}let e=D(i," ");if(e.length!==1&&e.length!==2)return!1;if(e.every(n=>n==="auto"||nt(n)||Rt(n))){r+=1;continue}}return r>0}var pn=["deg","rad","grad","turn"],dn=new RegExp(`^${fe.source}(${pn.join("|")})$`);function mn(t){return dn.test(t)}var gn=new RegExp(`^${fe.source} +${fe.source} +${fe.source}$`);function hn(t){return gn.test(t)}function T(t){let r=Number(t);return Number.isInteger(r)&&r>=0&&String(r)===String(t)}function Pt(t){let r=Number(t);return Number.isInteger(r)&&r>0&&String(r)===String(t)}function xe(t){return xr(t,.25)}function ot(t){return xr(t,.25)}function xr(t,r){let i=Number(t);return i>=0&&i%r===0&&String(i)===String(t)}var vn=new Set(["inset","inherit","initial","revert","unset"]),Ar=/^-?(\d+|\.\d+)(.*?)$/g;function Ue(t,r){return D(t,",").map(e=>{e=e.trim();let n=D(e," ").filter(c=>c.trim()!==""),s=null,a=null,f=null;for(let c of n)vn.has(c)||(Ar.test(c)?(a===null?a=c:f===null&&(f=c),Ar.lastIndex=0):s===null&&(s=c));if(a===null||f===null)return e;let u=r(s??"currentcolor");return s!==null?e.replace(s,u):`${e} ${u}`}).join(", ")}var wn=/^-?[a-z][a-zA-Z0-9/%._-]*$/,kn=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,at=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],Ot=class{utilities=new B(()=>[]);completions=new Map;static(r,i){this.utilities.get(r).push({kind:"static",compileFn:i})}functional(r,i,e){this.utilities.get(r).push({kind:"functional",compileFn:i,options:e})}has(r,i){return this.utilities.has(r)&&this.utilities.get(r).some(e=>e.kind===i)}get(r){return this.utilities.has(r)?this.utilities.get(r):[]}getCompletions(r){return this.completions.get(r)?.()??[]}suggest(r,i){this.completions.set(r,i)}keys(r){let i=[];for(let[e,n]of this.utilities.entries())for(let s of n)if(s.kind===r){i.push(e);break}return i}};function N(t,r,i){return F("@property",t,[l("syntax",i?`"${i}"`:'"*"'),l("inherits","false"),...r?[l("initial-value",r)]:[]])}function Q(t,r){if(r===null)return t;let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),r==="100%"?t:`color-mix(in oklab, ${t} ${r}, transparent)`}function Nr(t,r){let i=Number(r);return Number.isNaN(i)||(r=`${i*100}%`),`oklab(from ${t} l a b / ${r})`}function X(t,r,i){if(!r)return t;if(r.kind==="arbitrary")return Q(t,r.value);let e=i.resolve(r.value,["--opacity"]);return e?Q(t,e):ot(r.value)?Q(t,`${r.value}%`):null}function te(t,r,i){let e=null;switch(t.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=r.resolve(t.value.value,i);break}}return e?X(e,t.modifier,r):null}var $r=/(\d+)_(\d+)/g;function Vr(t){let r=new Ot;function i(o,p){function*h(k){for(let C of t.keysInNamespaces(k))yield C.replace($r,(O,$,E)=>`${$}.${E}`)}let A=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];r.suggest(o,()=>{let k=[];for(let C of p()){if(typeof C=="string"){k.push({values:[C],modifiers:[]});continue}let O=[...C.values??[],...h(C.valueThemeKeys??[])],$=[...C.modifiers??[],...h(C.modifierThemeKeys??[])];C.supportsFractions&&O.push(...A),C.hasDefaultValue&&O.unshift(null),k.push({supportsNegative:C.supportsNegative,values:O,modifiers:$})}return k})}function e(o,p){r.static(o,()=>p.map(h=>typeof h=="function"?h():l(h[0],h[1])))}function n(o,p){function h({negative:A}){return k=>{let C=null,O=null;if(k.value)if(k.value.kind==="arbitrary"){if(k.modifier)return;C=k.value.value,O=k.value.dataType}else{if(C=t.resolve(k.value.fraction??k.value.value,p.themeKeys??[]),C===null&&p.supportsFractions&&k.value.fraction){let[$,E]=D(k.value.fraction,"/");if(!T($)||!T(E))return;C=`calc(${k.value.fraction} * 100%)`}if(C===null&&A&&p.handleNegativeBareValue){if(C=p.handleNegativeBareValue(k.value),!C?.includes("/")&&k.modifier)return;if(C!==null)return p.handle(C,null)}if(C===null&&p.handleBareValue&&(C=p.handleBareValue(k.value),!C?.includes("/")&&k.modifier))return}else{if(k.modifier)return;C=p.defaultValue!==void 0?p.defaultValue:t.resolve(null,p.themeKeys??[])}if(C!==null)return p.handle(A?`calc(${C} * -1)`:C,O)}}p.supportsNegative&&r.functional(`-${o}`,h({negative:!0})),r.functional(o,h({negative:!1})),i(o,()=>[{supportsNegative:p.supportsNegative,valueThemeKeys:p.themeKeys??[],hasDefaultValue:p.defaultValue!==void 0&&p.defaultValue!==null,supportsFractions:p.supportsFractions}])}function s(o,p){r.functional(o,h=>{if(!h.value)return;let A=null;if(h.value.kind==="arbitrary"?(A=h.value.value,A=X(A,h.modifier,t)):A=te(h,t,p.themeKeys),A!==null)return p.handle(A)}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:p.themeKeys,modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}function a(o,p,h,{supportsNegative:A=!1,supportsFractions:k=!1}={}){A&&r.static(`-${o}-px`,()=>h("-1px")),r.static(`${o}-px`,()=>h("1px")),n(o,{themeKeys:p,supportsFractions:k,supportsNegative:A,defaultValue:null,handleBareValue:({value:C})=>{let O=t.resolve(null,["--spacing"]);return!O||!xe(C)?null:`calc(${O} * ${C})`},handleNegativeBareValue:({value:C})=>{let O=t.resolve(null,["--spacing"]);return!O||!xe(C)?null:`calc(${O} * -${C})`},handle:h}),i(o,()=>[{values:t.get(["--spacing"])?at:[],supportsNegative:A,supportsFractions:k,valueThemeKeys:p}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,p]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[p,"auto"]]),e(`${o}-full`,[[p,"100%"]]),e(`-${o}-full`,[[p,"-100%"]]),a(o,["--inset","--spacing"],h=>[l(p,h)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),n("z",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--z-index"],handle:o=>[l("z-index",o)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),n("order",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--order"],handle:o=>[l("order",o)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),n("col",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column"],handle:o=>[l("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),n("col-span",{handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),n("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[l("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),n("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[l("grid-column-end",o)]}),i("col-span",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),n("row",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row"],handle:o=>[l("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),n("row-span",{themeKeys:[],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),n("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[l("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),n("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>T(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[l("grid-row-end",o)]}),i("row-span",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,p]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[p,"auto"]]),a(o,["--margin","--spacing"],h=>[l(p,h)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),n("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",o)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),n("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[p,h]=D(o,"/");return!T(p)||!T(h)?null:o},handle:o=>[l("aspect-ratio",o)]});for(let[o,p]of[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",p],["height",p]]),e(`w-${o}`,[["width",p]]),e(`h-${o}`,[["height",p]]),e(`min-w-${o}`,[["min-width",p]]),e(`min-h-${o}`,[["min-height",p]]),e(`max-w-${o}`,[["max-width",p]]),e(`max-h-${o}`,[["max-height",p]]);e("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),e("w-auto",[["width","auto"]]),e("h-auto",[["height","auto"]]),e("min-w-auto",[["min-width","auto"]]),e("min-h-auto",[["min-height","auto"]]),e("h-lh",[["height","1lh"]]),e("min-h-lh",[["min-height","1lh"]]),e("max-h-lh",[["max-height","1lh"]]),e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],o=>[l("--tw-sort","size"),l("width",o),l("height",o)],{supportsFractions:!0});for(let[o,p,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(o,p,A=>[l(h,A)],{supportsFractions:!0});r.static("container",()=>{let o=[...t.namespace("--breakpoint").values()];o.sort((h,A)=>ye(h,A,"asc"));let p=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let h of o)p.push(F("@media",`(width >= ${h})`,[l("max-width",h)]));return p}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),r.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[l("flex",o.value.value)];if(o.value.fraction){let[p,h]=D(o.value.fraction,"/");return!T(p)||!T(h)?void 0:[l("flex",`calc(${o.value.fraction} * 100%)`)]}if(T(o.value.value))return o.modifier?void 0:[l("flex",o.value.value)]}}),i("flex",()=>[{supportsFractions:!0}]),n("shrink",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("flex-shrink",o)]}),n("grow",{defaultValue:"1",handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("flex-grow",o)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],o=>[l("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let f=()=>z([N("--tw-border-spacing-x","0","<length>"),N("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-x",o),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-x",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],o=>[f(),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),n("origin",{themeKeys:["--transform-origin"],handle:o=>[l("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),n("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[l("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),n("perspective",{themeKeys:["--perspective"],handle:o=>[l("perspective",o)]});let u=()=>z([N("--tw-translate-x","0"),N("--tw-translate-y","0"),N("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[u,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[u,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],o=>[u(),l("--tw-translate-x",o),l("--tw-translate-y",o),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[u,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[u,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${o}`,["--translate","--spacing"],p=>[u(),l(`--tw-translate-${o}`,p),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],o=>[u(),l("--tw-translate-z",o),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[u,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let c=()=>z([N("--tw-scale-x","1"),N("--tw-scale-y","1"),N("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function d({negative:o}){return p=>{if(!p.value||p.modifier)return;let h;return p.value.kind==="arbitrary"?(h=p.value.value,h=o?`calc(${h} * -1)`:h,[l("scale",h)]):(h=t.resolve(p.value.value,["--scale"]),!h&&T(p.value.value)&&(h=`${p.value.value}%`),h?(h=o?`calc(${h} * -1)`:h,[c(),l("--tw-scale-x",h),l("--tw-scale-y",h),l("--tw-scale-z",h),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}r.functional("-scale",d({negative:!0})),r.functional("scale",d({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])n(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:p})=>T(p)?`${p}%`:null,handle:p=>[c(),l(`--tw-scale-${o}`,p),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[c,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return p=>{if(!p.value||p.modifier)return;let h;if(p.value.kind==="arbitrary"){h=p.value.value;let A=p.value.dataType??Y(h,["angle","vector"]);if(A==="vector")return[l("rotate",`${h} var(--tw-rotate)`)];if(A!=="angle")return[l("rotate",o?`calc(${h} * -1)`:h)]}else if(h=t.resolve(p.value.value,["--rotate"]),!h&&T(p.value.value)&&(h=`${p.value.value}deg`),!h)return;return[l("rotate",o?`calc(${h} * -1)`:h)]}}r.functional("-rotate",m({negative:!0})),r.functional("rotate",m({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),p=()=>z([N("--tw-rotate-x"),N("--tw-rotate-y"),N("--tw-rotate-z"),N("--tw-skew-x"),N("--tw-skew-y")]);for(let h of["x","y","z"])n(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:A})=>T(A)?`${A}deg`:null,handle:A=>[p(),l(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${A})`),l("transform",o)]}),i(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);n("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[p(),l("--tw-skew-x",`skewX(${h})`),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),n("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[p(),l("--tw-skew-x",`skewX(${h})`),l("transform",o)]}),n("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>T(h)?`${h}deg`:null,handle:h=>[p(),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),r.functional("transform",h=>{if(h.modifier)return;let A=null;if(h.value?h.value.kind==="arbitrary"&&(A=h.value.value):A=o,A!==null)return[p(),l("transform",A)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);n("cursor",{themeKeys:["--cursor"],handle:o=>[l("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let g=()=>z([N("--tw-pan-x"),N("--tw-pan-y"),N("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[g,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[g,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[g,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let w=()=>z([N("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[w,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,p]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(o,["--scroll-margin","--spacing"],h=>[l(p,h)],{supportsNegative:!0});for(let[o,p]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(o,["--scroll-padding","--spacing"],h=>[l(p,h)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),n("list",{themeKeys:["--list-style-type"],handle:o=>[l("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),n("list-image",{themeKeys:["--list-style-image"],handle:o=>[l("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),n("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>T(o)?o:null,handle:o=>[l("columns",o)]}),i("columns",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),n("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[l("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),n("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[l("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),n("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>Pt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),n("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>Pt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-rows",o)]}),i("grid-cols",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(o,p)=>`${p+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],o=>[l("gap",o)]),a("gap-x",["--gap","--spacing"],o=>[l("column-gap",o)]),a("gap-y",["--gap","--spacing"],o=>[l("row-gap",o)]),a("space-x",["--space","--spacing"],o=>[z([N("--tw-space-x-reverse","0")]),M(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],o=>[z([N("--tw-space-y-reverse","0")]),M(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>z([N("--tw-space-x-reverse","0")]),()=>M(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>z([N("--tw-space-y-reverse","0")]),()=>M(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:o=>[l("accent-color",o)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:o=>[l("caret-color",o)]}),s("divide",{themeKeys:["--divide-color","--color"],handle:o=>[M(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[o,p]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,p.map(h=>[h,"0"])),e(`${o}-full`,p.map(h=>[h,"calc(infinity * 1px)"])),n(o,{themeKeys:["--radius"],handle:h=>p.map(A=>l(A,h))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let p=function(h,A){r.functional(h,k=>{if(!k.value){if(k.modifier)return;let C=t.get(["--default-border-width"])??"1px",O=A.width(C);return O?[o(),...O]:void 0}if(k.value.kind==="arbitrary"){let C=k.value.value;switch(k.value.dataType??Y(C,["color","line-width","length"])){case"line-width":case"length":{if(k.modifier)return;let $=A.width(C);return $?[o(),...$]:void 0}default:return C=X(C,k.modifier,t),C===null?void 0:A.color(C)}}{let C=te(k,t,["--border-color","--color"]);if(C)return A.color(C)}{if(k.modifier)return;let C=t.resolve(k.value.value,["--border-width"]);if(C){let O=A.width(C);return O?[o(),...O]:void 0}if(T(k.value.value)){let O=A.width(`${k.value.value}px`);return O?[o(),...O]:void 0}}}),i(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(k,C)=>`${C*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var _=p;let o=()=>z([N("--tw-border-style","solid")]);p("border",{width:h=>[l("border-style","var(--tw-border-style)"),l("border-width",h)],color:h=>[l("border-color",h)]}),p("border-x",{width:h=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",h)],color:h=>[l("border-inline-color",h)]}),p("border-y",{width:h=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",h)],color:h=>[l("border-block-color",h)]}),p("border-s",{width:h=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",h)],color:h=>[l("border-inline-start-color",h)]}),p("border-e",{width:h=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",h)],color:h=>[l("border-inline-end-color",h)]}),p("border-t",{width:h=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",h)],color:h=>[l("border-top-color",h)]}),p("border-r",{width:h=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",h)],color:h=>[l("border-right-color",h)]}),p("border-b",{width:h=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",h)],color:h=>[l("border-bottom-color",h)]}),p("border-l",{width:h=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",h)],color:h=>[l("border-left-color",h)]}),n("divide-x",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[z([N("--tw-divide-x-reverse","0")]),M(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),o(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),n("divide-y",{defaultValue:t.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>T(h)?`${h}px`:null,handle:h=>[z([N("--tw-divide-y-reverse","0")]),M(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),o(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>z([N("--tw-divide-x-reverse","0")]),()=>M(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>z([N("--tw-divide-y-reverse","0")]),()=>M(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])e(`divide-${h}`,[()=>M(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",h),l("border-style",h)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),n("bg-size",{handle(o){if(o)return[l("background-size",o)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),n("bg-position",{handle(o){if(o)return[l("background-position",o)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let h=function(C){let O="in oklab";if(C?.kind==="named")switch(C.value){case"longer":case"shorter":case"increasing":case"decreasing":O=`in oklch ${C.value} hue`;break;default:O=`in ${C.value}`}else C?.kind==="arbitrary"&&(O=C.value);return O},A=function({negative:C}){return O=>{if(!O.value)return;if(O.value.kind==="arbitrary"){if(O.modifier)return;let U=O.value.value;switch(O.value.dataType??Y(U,["angle"])){case"angle":return U=C?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return C?void 0:[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let $=O.value.value;if(!C&&p.has($))$=p.get($);else if(T($))$=C?`calc(${$}deg * -1)`:`${$}deg`;else return;let E=h(O.modifier);return[l("--tw-gradient-position",`${$}`),G("@supports (background-image: linear-gradient(in lab, red, red))",[l("--tw-gradient-position",`${$} ${E}`)]),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},k=function({negative:C}){return O=>{if(O.value?.kind==="arbitrary"){if(O.modifier)return;let U=O.value.value;return[l("--tw-gradient-position",U),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let $=h(O.modifier);if(!O.value)return[l("--tw-gradient-position",$),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let E=O.value.value;if(T(E))return E=C?`calc(${E}deg * -1)`:`${E}deg`,[l("--tw-gradient-position",`from ${E} ${$}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var H=h,j=A,W=k;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],p=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);r.functional("-bg-linear",A({negative:!0})),r.functional("bg-linear",A({negative:!1})),i("bg-linear",()=>[{values:[...p.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("-bg-conic",k({negative:!0})),r.functional("bg-conic",k({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),r.functional("bg-radial",C=>{if(!C.value){let O=h(C.modifier);return[l("--tw-gradient-position",O),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(C.value.kind==="arbitrary"){if(C.modifier)return;let O=C.value.value;return[l("--tw-gradient-position",O),l("background-image",`radial-gradient(var(--tw-gradient-stops,${O}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}r.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??Y(p,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("background-position",p)];case"bg-size":case"length":case"size":return o.modifier?void 0:[l("background-size",p)];case"image":case"url":return o.modifier?void 0:[l("background-image",p)];default:return p=X(p,o.modifier,t),p===null?void 0:[l("background-color",p)]}}{let p=te(o,t,["--background-color","--color"]);if(p)return[l("background-color",p)]}{if(o.modifier)return;let p=t.resolve(o.value.value,["--background-image"]);if(p)return[l("background-image",p)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>z([N("--tw-gradient-position"),N("--tw-gradient-from","#0000","<color>"),N("--tw-gradient-via","#0000","<color>"),N("--tw-gradient-to","#0000","<color>"),N("--tw-gradient-stops"),N("--tw-gradient-via-stops"),N("--tw-gradient-from-position","0%","<length-percentage>"),N("--tw-gradient-via-position","50%","<length-percentage>"),N("--tw-gradient-to-position","100%","<length-percentage>")]);function x(o,p){r.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??Y(A,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:p.position(A);default:return A=X(A,h.modifier,t),A===null?void 0:p.color(A)}}{let A=te(h,t,["--background-color","--color"]);if(A)return p.color(A)}{if(h.modifier)return;let A=t.resolve(h.value.value,["--gradient-color-stop-positions"]);if(A)return p.position(A);if(h.value.value[h.value.value.length-1]==="%"&&T(h.value.value.slice(0,-1)))return p.position(h.value.value)}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}x("from",{color:o=>[v(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),x("via",{color:o=>[v(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",o),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[v(),l("--tw-gradient-via-position",o)]}),x("to",{color:o=>[v(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-to-position",o)]}),e("mask-none",[["mask-image","none"]]),r.functional("mask",o=>{if(!o.value||o.modifier||o.value.kind!=="arbitrary")return;let p=o.value.value;switch(o.value.dataType??Y(p,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("mask-position",p)];case"bg-size":case"length":case"size":return[l("mask-size",p)];case"image":case"url":default:return[l("mask-image",p)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),n("mask-size",{handle(o){if(o)return[l("mask-size",o)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),n("mask-position",{handle(o){if(o)return[l("mask-position",o)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let y=()=>z([N("--tw-mask-linear","linear-gradient(#fff, #fff)"),N("--tw-mask-radial","linear-gradient(#fff, #fff)"),N("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function V(o,p){r.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??Y(A,["length","percentage","color"])){case"color":return A=X(A,h.modifier,t),A===null?void 0:p.color(A);case"percentage":return h.modifier||!T(A.slice(0,-1))?void 0:p.position(A);default:return h.modifier?void 0:p.position(A)}}{let A=te(h,t,["--background-color","--color"]);if(A)return p.color(A)}{if(h.modifier)return;let A=Y(h.value.value,["number","percentage"]);if(!A)return;switch(A){case"number":{let k=t.resolve(null,["--spacing"]);return!k||!xe(h.value.value)?void 0:p.position(`calc(${k} * ${h.value.value})`)}case"percentage":return T(h.value.value.slice(0,-1))?p.position(h.value.value):void 0;default:return}}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(o,()=>[{values:Array.from({length:21},(h,A)=>`${A*5}%`)},{values:t.get(["--spacing"])?at:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}let b=()=>z([N("--tw-mask-left","linear-gradient(#fff, #fff)"),N("--tw-mask-right","linear-gradient(#fff, #fff)"),N("--tw-mask-bottom","linear-gradient(#fff, #fff)"),N("--tw-mask-top","linear-gradient(#fff, #fff)")]);function S(o,p,h){V(o,{color(A){let k=[y(),b(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let C of["top","right","bottom","left"])h[C]&&(k.push(l(`--tw-mask-${C}`,`linear-gradient(to ${C}, var(--tw-mask-${C}-from-color) var(--tw-mask-${C}-from-position), var(--tw-mask-${C}-to-color) var(--tw-mask-${C}-to-position))`)),k.push(z([N(`--tw-mask-${C}-from-position`,"0%"),N(`--tw-mask-${C}-to-position`,"100%"),N(`--tw-mask-${C}-from-color`,"black"),N(`--tw-mask-${C}-to-color`,"transparent")])),k.push(l(`--tw-mask-${C}-${p}-color`,A)));return k},position(A){let k=[y(),b(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let C of["top","right","bottom","left"])h[C]&&(k.push(l(`--tw-mask-${C}`,`linear-gradient(to ${C}, var(--tw-mask-${C}-from-color) var(--tw-mask-${C}-from-position), var(--tw-mask-${C}-to-color) var(--tw-mask-${C}-to-position))`)),k.push(z([N(`--tw-mask-${C}-from-position`,"0%"),N(`--tw-mask-${C}-to-position`,"100%"),N(`--tw-mask-${C}-from-color`,"black"),N(`--tw-mask-${C}-to-color`,"transparent")])),k.push(l(`--tw-mask-${C}-${p}-position`,A)));return k}})}S("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),S("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let R=()=>z([N("--tw-mask-linear-position","0deg"),N("--tw-mask-linear-from-position","0%"),N("--tw-mask-linear-to-position","100%"),N("--tw-mask-linear-from-color","black"),N("--tw-mask-linear-to-color","transparent")]);n("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return T(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return T(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[y(),R(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),l("--tw-mask-linear-position",o)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),V("mask-linear-from",{color:o=>[y(),R(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-color",o)],position:o=>[y(),R(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-position",o)]}),V("mask-linear-to",{color:o=>[y(),R(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-color",o)],position:o=>[y(),R(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-position",o)]});let L=()=>z([N("--tw-mask-radial-from-position","0%"),N("--tw-mask-radial-to-position","100%"),N("--tw-mask-radial-from-color","black"),N("--tw-mask-radial-to-color","transparent"),N("--tw-mask-radial-shape","ellipse"),N("--tw-mask-radial-size","farthest-corner"),N("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),n("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[l("--tw-mask-radial-position",o)]}),n("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[y(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),l("--tw-mask-radial-size",o)]}),V("mask-radial-from",{color:o=>[y(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-color",o)],position:o=>[y(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-position",o)]}),V("mask-radial-to",{color:o=>[y(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-color",o)],position:o=>[y(),L(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-position",o)]});let P=()=>z([N("--tw-mask-conic-position","0deg"),N("--tw-mask-conic-from-position","0%"),N("--tw-mask-conic-to-position","100%"),N("--tw-mask-conic-from-color","black"),N("--tw-mask-conic-to-color","transparent")]);n("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return T(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return T(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[y(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),l("--tw-mask-conic-position",o)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),V("mask-conic-from",{color:o=>[y(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-color",o)],position:o=>[y(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-position",o)]}),V("mask-conic-to",{color:o=>[y(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-color",o)],position:o=>[y(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),r.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let h=X(o.value.value,o.modifier,t);return h===null?void 0:[l("fill",h)]}let p=te(o,t,["--fill","--color"]);if(p)return[l("fill",p)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)}]),e("stroke-none",[["stroke","none"]]),r.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??Y(p,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[l("stroke-width",p)];default:return p=X(o.value.value,o.modifier,t),p===null?void 0:[l("stroke",p)]}}{let p=te(o,t,["--stroke","--color"]);if(p)return[l("stroke",p)]}{let p=t.resolve(o.value.value,["--stroke-width"]);if(p)return[l("stroke-width",p)];if(T(o.value.value))return[l("stroke-width",o.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-top",[["object-position","top"]]),e("object-top-left",[["object-position","left top"]]),e("object-top-right",[["object-position","right top"]]),e("object-bottom",[["object-position","bottom"]]),e("object-bottom-left",[["object-position","left bottom"]]),e("object-bottom-right",[["object-position","right bottom"]]),e("object-left",[["object-position","left"]]),e("object-right",[["object-position","right"]]),e("object-center",[["object-position","center"]]),n("object",{themeKeys:["--object-position"],handle:o=>[l("object-position",o)]});for(let[o,p]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(o,["--padding","--spacing"],h=>[l(p,h)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],o=>[l("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),n("align",{themeKeys:[],handle:o=>[l("vertical-align",o)]}),r.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??Y(p,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",p)];default:return[z([N("--tw-font-weight")]),l("--tw-font-weight",p),l("font-weight",p)]}}{let p=t.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(p){let[h,A={}]=p;return[l("font-family",h),l("font-feature-settings",A["--font-feature-settings"]),l("font-variation-settings",A["--font-variation-settings"])]}}{let p=t.resolve(o.value.value,["--font-weight"]);if(p)return[z([N("--tw-font-weight")]),l("--tw-font-weight",p),l("font-weight",p)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),n("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let p=Number(o.slice(0,-1));return!T(p)||Number.isNaN(p)||p<50||p>200?null:o},handle:o=>[l("font-stretch",o)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[M("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),r.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??Y(p,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[l("text-decoration-thickness",p)];default:return p=X(p,o.modifier,t),p===null?void 0:[l("text-decoration-color",p)]}}{let p=t.resolve(o.value.value,["--text-decoration-thickness"]);if(p)return o.modifier?void 0:[l("text-decoration-thickness",p)];if(T(o.value.value))return o.modifier?void 0:[l("text-decoration-thickness",`${o.value.value}px`)]}{let p=te(o,t,["--text-decoration-color","--color"]);if(p)return[l("text-decoration-color",p)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),n("animate",{themeKeys:["--animate"],handle:o=>[l("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),p=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>z([N("--tw-blur"),N("--tw-brightness"),N("--tw-contrast"),N("--tw-grayscale"),N("--tw-hue-rotate"),N("--tw-invert"),N("--tw-opacity"),N("--tw-saturate"),N("--tw-sepia"),N("--tw-drop-shadow"),N("--tw-drop-shadow-color"),N("--tw-drop-shadow-alpha","100%","<percentage>"),N("--tw-drop-shadow-size")]),A=()=>z([N("--tw-backdrop-blur"),N("--tw-backdrop-brightness"),N("--tw-backdrop-contrast"),N("--tw-backdrop-grayscale"),N("--tw-backdrop-hue-rotate"),N("--tw-backdrop-invert"),N("--tw-backdrop-opacity"),N("--tw-backdrop-saturate"),N("--tw-backdrop-sepia")]);r.functional("filter",k=>{if(!k.modifier){if(k.value===null)return[h(),l("filter",o)];if(k.value.kind==="arbitrary")return[l("filter",k.value.value)];switch(k.value.value){case"none":return[l("filter","none")]}}}),r.functional("backdrop-filter",k=>{if(!k.modifier){if(k.value===null)return[A(),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)];if(k.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",k.value.value),l("backdrop-filter",k.value.value)];switch(k.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),n("blur",{themeKeys:["--blur"],handle:k=>[h(),l("--tw-blur",`blur(${k})`),l("filter",o)]}),e("blur-none",[h,["--tw-blur"," "],["filter",o]]),n("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:k=>[A(),l("--tw-backdrop-blur",`blur(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),e("backdrop-blur-none",[A,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",p],["backdrop-filter",p]]),n("brightness",{themeKeys:["--brightness"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[h(),l("--tw-brightness",`brightness(${k})`),l("filter",o)]}),n("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[A(),l("--tw-backdrop-brightness",`brightness(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),n("contrast",{themeKeys:["--contrast"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[h(),l("--tw-contrast",`contrast(${k})`),l("filter",o)]}),n("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[A(),l("--tw-backdrop-contrast",`contrast(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),n("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),l("--tw-grayscale",`grayscale(${k})`),l("filter",o)]}),n("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),l("--tw-backdrop-grayscale",`grayscale(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),n("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:k})=>T(k)?`${k}deg`:null,handle:k=>[h(),l("--tw-hue-rotate",`hue-rotate(${k})`),l("filter",o)]}),n("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:k})=>T(k)?`${k}deg`:null,handle:k=>[A(),l("--tw-backdrop-hue-rotate",`hue-rotate(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),n("invert",{themeKeys:["--invert"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),l("--tw-invert",`invert(${k})`),l("filter",o)]}),n("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),l("--tw-backdrop-invert",`invert(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),n("saturate",{themeKeys:["--saturate"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[h(),l("--tw-saturate",`saturate(${k})`),l("filter",o)]}),n("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,handle:k=>[A(),l("--tw-backdrop-saturate",`saturate(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),n("sepia",{themeKeys:["--sepia"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[h(),l("--tw-sepia",`sepia(${k})`),l("filter",o)]}),n("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:k})=>T(k)?`${k}%`:null,defaultValue:"100%",handle:k=>[A(),l("--tw-backdrop-sepia",`sepia(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",o]]),r.functional("drop-shadow",k=>{let C;if(k.modifier&&(k.modifier.kind==="arbitrary"?C=k.modifier.value:T(k.modifier.value)&&(C=`${k.modifier.value}%`)),!k.value){let O=t.get(["--drop-shadow"]),$=t.resolve(null,["--drop-shadow"]);return O===null||$===null?void 0:[h(),l("--tw-drop-shadow-alpha",C),...lt("--tw-drop-shadow-size",O,C,E=>`var(--tw-drop-shadow-color, ${E})`),l("--tw-drop-shadow",D($,",").map(E=>`drop-shadow(${E})`).join(" ")),l("filter",o)]}if(k.value.kind==="arbitrary"){let O=k.value.value;switch(k.value.dataType??Y(O,["color"])){case"color":return O=X(O,k.modifier,t),O===null?void 0:[h(),l("--tw-drop-shadow-color",Q(O,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return k.modifier&&!C?void 0:[h(),l("--tw-drop-shadow-alpha",C),...lt("--tw-drop-shadow-size",O,C,E=>`var(--tw-drop-shadow-color, ${E})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]}}{let O=t.get([`--drop-shadow-${k.value.value}`]),$=t.resolve(k.value.value,["--drop-shadow"]);if(O&&$)return k.modifier&&!C?void 0:C?[h(),l("--tw-drop-shadow-alpha",C),...lt("--tw-drop-shadow-size",O,C,E=>`var(--tw-drop-shadow-color, ${E})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]:[h(),l("--tw-drop-shadow-alpha",C),...lt("--tw-drop-shadow-size",O,C,E=>`var(--tw-drop-shadow-color, ${E})`),l("--tw-drop-shadow",D($,",").map(E=>`drop-shadow(${E})`).join(" ")),l("filter",o)]}{let O=te(k,t,["--drop-shadow-color","--color"]);if(O)return O==="inherit"?[h(),l("--tw-drop-shadow-color","inherit"),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[h(),l("--tw-drop-shadow-color",Q(O,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(k,C)=>`${C*5}`)},{valueThemeKeys:["--drop-shadow"]}]),n("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:k})=>ot(k)?`${k}%`:null,handle:k=>[A(),l("--tw-backdrop-opacity",`opacity(${k})`),l("-webkit-backdrop-filter",p),l("backdrop-filter",p)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(k,C)=>`${C*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${t.resolve(null,["--default-transition-timing-function"])??"ease"})`,p=`var(--tw-duration, ${t.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",p]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",p]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",p]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",p]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",p]]),n("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:h=>[l("transition-property",h),l("transition-timing-function",o),l("transition-duration",p)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),n("delay",{handleBareValue:({value:h})=>T(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[l("transition-delay",h)]});{let h=()=>z([N("--tw-duration")]);e("duration-initial",[h,["--tw-duration","initial"]]),r.functional("duration",A=>{if(A.modifier||!A.value)return;let k=null;if(A.value.kind==="arbitrary"?k=A.value.value:(k=t.resolve(A.value.fraction??A.value.value,["--transition-duration"]),k===null&&T(A.value.value)&&(k=`${A.value.value}ms`)),k!==null)return[h(),l("--tw-duration",k),l("transition-duration",k)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>z([N("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),n("ease",{themeKeys:["--ease"],handle:p=>[o(),l("--tw-ease",p),l("transition-timing-function",p)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),n("will-change",{themeKeys:[],handle:o=>[l("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),n("content",{themeKeys:[],handle:o=>[z([N("--tw-content",'""')]),l("--tw-content",o),l("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",p=()=>z([N("--tw-contain-size"),N("--tw-contain-layout"),N("--tw-contain-paint"),N("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[p,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[p,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[p,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[p,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[p,["--tw-contain-style","style"],["contain",o]]),n("contain",{themeKeys:[],handle:h=>[l("contain",h)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>z([N("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],o=>[z([N("--tw-leading")]),l("--tw-leading",o),l("line-height",o)]),n("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[z([N("--tw-tracking")]),l("--tw-tracking",o),l("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",p=()=>z([N("--tw-ordinal"),N("--tw-slashed-zero"),N("--tw-numeric-figure"),N("--tw-numeric-spacing"),N("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[p,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[p,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[p,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[p,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[p,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[p,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[p,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[p,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>z([N("--tw-outline-style","solid")]);r.static("outline-hidden",()=>[l("--tw-outline-style","none"),l("outline-style","none"),F("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),r.functional("outline",p=>{if(p.value===null){if(p.modifier)return;let h=t.get(["--default-outline-width"])??"1px";return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)]}if(p.value.kind==="arbitrary"){let h=p.value.value;switch(p.value.dataType??Y(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return p.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];default:return h=X(h,p.modifier,t),h===null?void 0:[l("outline-color",h)]}}{let h=te(p,t,["--outline-color","--color"]);if(h)return[l("outline-color",h)]}{if(p.modifier)return;let h=t.resolve(p.value.value,["--outline-width"]);if(h)return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];if(T(p.value.value))return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${p.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(p,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),n("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:p})=>T(p)?`${p}px`:null,handle:p=>[l("outline-offset",p)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}n("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>ot(o)?`${o}%`:null,handle:o=>[l("opacity",o)]}),i("opacity",()=>[{values:Array.from({length:21},(o,p)=>`${p*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),n("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>T(o)?`${o}px`:null,handle:o=>[l("text-underline-offset",o)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),r.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let p=o.value.value;switch(o.value.dataType??Y(p,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let A=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!A&&xe(o.modifier.value)){let k=t.resolve(null,["--spacing"]);if(!k)return null;A=`calc(${k} * ${o.modifier.value})`}return!A&&o.modifier.value==="none"&&(A="1"),A?[l("font-size",p),l("line-height",A)]:null}return[l("font-size",p)]}default:return p=X(p,o.modifier,t),p===null?void 0:[l("color",p)]}}{let p=te(o,t,["--text-color","--color"]);if(p)return[l("color",p)]}{let p=t.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(p){let[h,A={}]=Array.isArray(p)?p:[p];if(o.modifier){let k=o.modifier.kind==="arbitrary"?o.modifier.value:t.resolve(o.modifier.value,["--leading"]);if(!k&&xe(o.modifier.value)){let O=t.resolve(null,["--spacing"]);if(!O)return null;k=`calc(${O} * ${o.modifier.value})`}if(!k&&o.modifier.value==="none"&&(k="1"),!k)return null;let C=[l("font-size",h)];return k&&C.push(l("line-height",k)),C}return typeof A=="string"?[l("font-size",h),l("line-height",A)]:[l("font-size",h),l("line-height",A["--line-height"]?`var(--tw-leading, ${A["--line-height"]})`:void 0),l("letter-spacing",A["--letter-spacing"]?`var(--tw-tracking, ${A["--letter-spacing"]})`:void 0),l("font-weight",A["--font-weight"]?`var(--tw-font-weight, ${A["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let K=()=>z([N("--tw-text-shadow-color"),N("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[K,["--tw-text-shadow-color","initial"]]),r.functional("text-shadow",o=>{let p;if(o.modifier&&(o.modifier.kind==="arbitrary"?p=o.modifier.value:T(o.modifier.value)&&(p=`${o.modifier.value}%`)),!o.value){let h=t.get(["--text-shadow"]);return h===null?void 0:[K(),l("--tw-text-shadow-alpha",p),...pe("text-shadow",h,p,A=>`var(--tw-text-shadow-color, ${A})`)]}if(o.value.kind==="arbitrary"){let h=o.value.value;switch(o.value.dataType??Y(h,["color"])){case"color":return h=X(h,o.modifier,t),h===null?void 0:[K(),l("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))];default:return[K(),l("--tw-text-shadow-alpha",p),...pe("text-shadow",h,p,k=>`var(--tw-text-shadow-color, ${k})`)]}}switch(o.value.value){case"none":return o.modifier?void 0:[K(),l("text-shadow","none")];case"inherit":return o.modifier?void 0:[K(),l("--tw-text-shadow-color","inherit")]}{let h=t.get([`--text-shadow-${o.value.value}`]);if(h)return[K(),l("--tw-text-shadow-alpha",p),...pe("text-shadow",h,p,A=>`var(--tw-text-shadow-color, ${A})`)]}{let h=te(o,t,["--text-shadow-color","--color"]);if(h)return[K(),l("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`),hasDefaultValue:t.get(["--text-shadow"])!==null}]);{let k=function($){return`var(--tw-ring-inset,) 0 0 0 calc(${$} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${A})`},C=function($){return`inset 0 0 0 ${$} var(--tw-inset-ring-color, currentcolor)`};var J=k,ie=C;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),p="0 0 #0000",h=()=>z([N("--tw-shadow",p),N("--tw-shadow-color"),N("--tw-shadow-alpha","100%","<percentage>"),N("--tw-inset-shadow",p),N("--tw-inset-shadow-color"),N("--tw-inset-shadow-alpha","100%","<percentage>"),N("--tw-ring-color"),N("--tw-ring-shadow",p),N("--tw-inset-ring-color"),N("--tw-inset-ring-shadow",p),N("--tw-ring-inset"),N("--tw-ring-offset-width","0px","<length>"),N("--tw-ring-offset-color","#fff"),N("--tw-ring-offset-shadow",p)]);e("shadow-initial",[h,["--tw-shadow-color","initial"]]),r.functional("shadow",$=>{let E;if($.modifier&&($.modifier.kind==="arbitrary"?E=$.modifier.value:T($.modifier.value)&&(E=`${$.modifier.value}%`)),!$.value){let U=t.get(["--shadow"]);return U===null?void 0:[h(),l("--tw-shadow-alpha",E),...pe("--tw-shadow",U,E,ae=>`var(--tw-shadow-color, ${ae})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??Y(U,["color"])){case"color":return U=X(U,$.modifier,t),U===null?void 0:[h(),l("--tw-shadow-color",Q(U,"var(--tw-shadow-alpha)"))];default:return[h(),l("--tw-shadow-alpha",E),...pe("--tw-shadow",U,E,wt=>`var(--tw-shadow-color, ${wt})`),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[h(),l("--tw-shadow",p),l("box-shadow",o)];case"inherit":return $.modifier?void 0:[h(),l("--tw-shadow-color","inherit")]}{let U=t.get([`--shadow-${$.value.value}`]);if(U)return[h(),l("--tw-shadow-alpha",E),...pe("--tw-shadow",U,E,ae=>`var(--tw-shadow-color, ${ae})`),l("box-shadow",o)]}{let U=te($,t,["--box-shadow-color","--color"]);if(U)return[h(),l("--tw-shadow-color",Q(U,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,E)=>`${E*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},($,E)=>`${E*5}`),hasDefaultValue:t.get(["--shadow"])!==null}]),e("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),r.functional("inset-shadow",$=>{let E;if($.modifier&&($.modifier.kind==="arbitrary"?E=$.modifier.value:T($.modifier.value)&&(E=`${$.modifier.value}%`)),!$.value){let U=t.get(["--inset-shadow"]);return U===null?void 0:[h(),l("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",U,E,ae=>`var(--tw-inset-shadow-color, ${ae})`),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let U=$.value.value;switch($.value.dataType??Y(U,["color"])){case"color":return U=X(U,$.modifier,t),U===null?void 0:[h(),l("--tw-inset-shadow-color",Q(U,"var(--tw-inset-shadow-alpha)"))];default:return[h(),l("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",U,E,wt=>`var(--tw-inset-shadow-color, ${wt})`,"inset "),l("box-shadow",o)]}}switch($.value.value){case"none":return $.modifier?void 0:[h(),l("--tw-inset-shadow",p),l("box-shadow",o)];case"inherit":return $.modifier?void 0:[h(),l("--tw-inset-shadow-color","inherit")]}{let U=t.get([`--inset-shadow-${$.value.value}`]);if(U)return[h(),l("--tw-inset-shadow-alpha",E),...pe("--tw-inset-shadow",U,E,ae=>`var(--tw-inset-shadow-color, ${ae})`),l("box-shadow",o)]}{let U=te($,t,["--box-shadow-color","--color"]);if(U)return[h(),l("--tw-inset-shadow-color",Q(U,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},($,E)=>`${E*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},($,E)=>`${E*5}`),hasDefaultValue:t.get(["--inset-shadow"])!==null}]),e("ring-inset",[h,["--tw-ring-inset","inset"]]);let A=t.get(["--default-ring-color"])??"currentcolor";r.functional("ring",$=>{if(!$.value){if($.modifier)return;let E=t.get(["--default-ring-width"])??"1px";return[h(),l("--tw-ring-shadow",k(E)),l("box-shadow",o)]}if($.value.kind==="arbitrary"){let E=$.value.value;switch($.value.dataType??Y(E,["color","length"])){case"length":return $.modifier?void 0:[h(),l("--tw-ring-shadow",k(E)),l("box-shadow",o)];default:return E=X(E,$.modifier,t),E===null?void 0:[l("--tw-ring-color",E)]}}{let E=te($,t,["--ring-color","--color"]);if(E)return[l("--tw-ring-color",E)]}{if($.modifier)return;let E=t.resolve($.value.value,["--ring-width"]);if(E===null&&T($.value.value)&&(E=`${$.value.value}px`),E)return[h(),l("--tw-ring-shadow",k(E)),l("box-shadow",o)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,E)=>`${E*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),r.functional("inset-ring",$=>{if(!$.value)return $.modifier?void 0:[h(),l("--tw-inset-ring-shadow",C("1px")),l("box-shadow",o)];if($.value.kind==="arbitrary"){let E=$.value.value;switch($.value.dataType??Y(E,["color","length"])){case"length":return $.modifier?void 0:[h(),l("--tw-inset-ring-shadow",C(E)),l("box-shadow",o)];default:return E=X(E,$.modifier,t),E===null?void 0:[l("--tw-inset-ring-color",E)]}}{let E=te($,t,["--ring-color","--color"]);if(E)return[l("--tw-inset-ring-color",E)]}{if($.modifier)return;let E=t.resolve($.value.value,["--ring-width"]);if(E===null&&T($.value.value)&&(E=`${$.value.value}px`),E)return[h(),l("--tw-inset-ring-shadow",C(E)),l("box-shadow",o)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},($,E)=>`${E*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let O="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";r.functional("ring-offset",$=>{if($.value){if($.value.kind==="arbitrary"){let E=$.value.value;switch($.value.dataType??Y(E,["color","length"])){case"length":return $.modifier?void 0:[l("--tw-ring-offset-width",E),l("--tw-ring-offset-shadow",O)];default:return E=X(E,$.modifier,t),E===null?void 0:[l("--tw-ring-offset-color",E)]}}{let E=t.resolve($.value.value,["--ring-offset-width"]);if(E)return $.modifier?void 0:[l("--tw-ring-offset-width",E),l("--tw-ring-offset-shadow",O)];if(T($.value.value))return $.modifier?void 0:[l("--tw-ring-offset-width",`${$.value.value}px`),l("--tw-ring-offset-shadow",O)]}{let E=te($,t,["--ring-offset-color","--color"]);if(E)return[l("--tw-ring-offset-color",E)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,p)=>`${p*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),r.functional("@container",o=>{let p=null;if(o.value===null?p="inline-size":o.value.kind==="arbitrary"?p=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(p="normal"),p!==null)return o.modifier?[l("container-type",p),l("container-name",o.modifier.value)]:[l("container-type",p)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),r}var _t=["number","integer","ratio","percentage"];function Sr(t){let r=t.params;return kn.test(r)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};I(t.nodes,n=>{if(n.kind!=="declaration"||!n.value||!n.value.includes("--value(")&&!n.value.includes("--modifier("))return;let s=q(n.value);ee(s,a=>{if(a.kind!=="function")return;if(a.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return ee(a.nodes,u=>{if(u.kind!=="function"||u.value!=="--value"&&u.value!=="--modifier")return;let c=u.value;for(let d of u.nodes)if(d.kind==="word"){if(d.value==="integer")e[c].usedSpacingInteger||=!0;else if(d.value==="number"&&(e[c].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(a.value!=="--value"&&a.value!=="--modifier")return;let f=D(Z(a.nodes),",");for(let[u,c]of f.entries())c=c.replace(/\\\*/g,"*"),c=c.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),c=c.replace(/\s+/g,""),c=c.replace(/(-\*){2,}/g,"-*"),c[0]==="-"&&c[1]==="-"&&!c.includes("-*")&&(c+="-*"),f[u]=c;a.nodes=q(f.join(","));for(let u of a.nodes)if(u.kind==="word"&&(u.value[0]==='"'||u.value[0]==="'")&&u.value[0]===u.value[u.value.length-1]){let c=u.value.slice(1,-1);e[a.value].literals.add(c)}else if(u.kind==="word"&&u.value[0]==="-"&&u.value[1]==="-"){let c=u.value.replace(/-\*.*$/g,"");e[a.value].themeKeys.add(c)}else if(u.kind==="word"&&!(u.value[0]==="["&&u.value[u.value.length-1]==="]")&&!_t.includes(u.value)){console.warn(`Unsupported bare value data type: "${u.value}".
Only valid data types are: ${_t.map(x=>`"${x}"`).join(", ")}.
`);let c=u.value,d=structuredClone(a),m="\xB6";ee(d.nodes,(x,{replaceWith:y})=>{x.kind==="word"&&x.value===c&&y({kind:"word",value:m})});let g="^".repeat(Z([u]).length),w=Z([d]).indexOf(m),v=["```css",Z([a])," ".repeat(w)+g,"```"].join(`
`);console.warn(v)}}),n.value=Z(s)}),i.utilities.functional(r.slice(0,-2),n=>{let s=structuredClone(t),a=n.value,f=n.modifier;if(a===null)return;let u=!1,c=!1,d=!1,m=!1,g=new Map,w=!1;if(I([s],(v,{parent:x,replaceWith:y})=>{if(x?.kind!=="rule"&&x?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let V=q(v.value);(ee(V,(S,{replaceWith:R})=>{if(S.kind==="function"){if(S.value==="--value"){u=!0;let L=Cr(a,S,i);return L?(c=!0,L.ratio?w=!0:g.set(v,x),R(L.nodes),1):(u||=!1,y([]),2)}else if(S.value==="--modifier"){if(f===null)return y([]),2;d=!0;let L=Cr(f,S,i);return L?(m=!0,R(L.nodes),1):(d||=!1,y([]),2)}}})??0)===0&&(v.value=Z(V))}),u&&!c||d&&!m||w&&m||f&&!w&&!m)return null;if(w)for(let[v,x]of g){let y=x.nodes.indexOf(v);y!==-1&&x.nodes.splice(y,1)}return s.nodes}),i.utilities.suggest(r.slice(0,-2),()=>{let n=[],s=[];for(let[a,{literals:f,usedSpacingNumber:u,usedSpacingInteger:c,themeKeys:d}]of[[n,e["--value"]],[s,e["--modifier"]]]){for(let m of f)a.push(m);if(u)a.push(...at);else if(c)for(let m of at)T(m)&&a.push(m);for(let m of i.theme.keysInNamespaces(d))a.push(m.replace($r,(g,w,v)=>`${w}.${v}`))}return[{values:n,modifiers:s}]})}:wn.test(r)?i=>{i.utilities.static(r,()=>structuredClone(t.nodes))}:null}function Cr(t,r,i){for(let e of r.nodes){if(t.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===t.value)return{nodes:q(t.value)};if(t.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let n=e.value;if(n.endsWith("-*")){n=n.slice(0,-2);let s=i.theme.resolve(t.value,[n]);if(s)return{nodes:q(s)}}else{let s=n.split("-*");if(s.length<=1)continue;let a=[s.shift()],f=i.theme.resolveWith(t.value,a,s);if(f){let[,u={}]=f;{let c=u[s.pop()];if(c)return{nodes:q(c)}}}}}else if(t.kind==="named"&&e.kind==="word"){if(!_t.includes(e.value))continue;let n=e.value==="ratio"&&"fraction"in t?t.fraction:t.value;if(!n)continue;let s=Y(n,[e.value]);if(s===null)continue;if(s==="ratio"){let[a,f]=D(n,"/");if(!T(a)||!T(f))continue}else{if(s==="number"&&!xe(n))continue;if(s==="percentage"&&!T(n.slice(0,-1)))continue}return{nodes:q(n),ratio:s==="ratio"}}else if(t.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let n=e.value.slice(1,-1);if(n==="*")return{nodes:q(t.value)};if("dataType"in t&&t.dataType&&t.dataType!==n)continue;if("dataType"in t&&t.dataType)return{nodes:q(t.value)};if(Y(t.value,[n])!==null)return{nodes:q(t.value)}}}}function pe(t,r,i,e,n=""){let s=!1,a=Ue(r,u=>i==null?e(u):u.startsWith("current")?e(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(Nr(u,i))));function f(u){return n?D(u,",").map(c=>n+c).join(","):u}return s?[l(t,f(Ue(r,e))),G("@supports (color: lab(from red l a b))",[l(t,f(a))])]:[l(t,f(a))]}function lt(t,r,i,e,n=""){let s=!1,a=D(r,",").map(f=>Ue(f,u=>i==null?e(u):u.startsWith("current")?e(Q(u,i)):((u.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(Nr(u,i))))).map(f=>`drop-shadow(${f})`).join(" ");return s?[l(t,n+D(r,",").map(f=>`drop-shadow(${Ue(f,e)})`).join(" ")),G("@supports (color: lab(from red l a b))",[l(t,n+a)])]:[l(t,n+a)]}var Dt={"--alpha":bn,"--spacing":yn,"--theme":xn,theme:An};function bn(t,r,i,...e){let[n,s]=D(i,"/").map(a=>a.trim());if(!n||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);return Q(n,s)}function yn(t,r,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let n=t.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${i})`}function xn(t,r,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;i.endsWith(" inline")&&(n=!0,i=i.slice(0,-7)),r.kind==="at-rule"&&(n=!0);let s=t.resolveThemeValue(i,n);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let a=e.join(", ");if(a==="initial")return s;if(s==="initial")return a;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let f=q(s);return Nn(f,a),Z(f)}return s}function An(t,r,i,...e){i=Cn(i);let n=t.resolveThemeValue(i);if(!n&&e.length>0)return e.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var Er=new RegExp(Object.keys(Dt).map(t=>`${t}\\(`).join("|"));function Ve(t,r){let i=0;return I(t,e=>{if(e.kind==="declaration"&&e.value&&Er.test(e.value)){i|=8,e.value=Tr(e.value,e,r);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&Er.test(e.params)&&(i|=8,e.params=Tr(e.params,e,r))}),i}function Tr(t,r,i){let e=q(t);return ee(e,(n,{replaceWith:s})=>{if(n.kind==="function"&&n.value in Dt){let a=D(Z(n.nodes).trim(),",").map(u=>u.trim()),f=Dt[n.value](i,r,...a);return s(q(f))}}),Z(e)}function Cn(t){if(t[0]!=="'"&&t[0]!=='"')return t;let r="",i=t[0];for(let e=1;e<t.length-1;e++){let n=t[e],s=t[e+1];n==="\\"&&(s===i||s==="\\")?(r+=s,e++):r+=n}return r}function Nn(t,r){ee(t,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${r}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=r)}})}function st(t,r){let i=t.length,e=r.length,n=i<e?i:e;for(let s=0;s<n;s++){let a=t.charCodeAt(s),f=r.charCodeAt(s);if(a>=48&&a<=57&&f>=48&&f<=57){let u=s,c=s+1,d=s,m=s+1;for(a=t.charCodeAt(c);a>=48&&a<=57;)a=t.charCodeAt(++c);for(f=r.charCodeAt(m);f>=48&&f<=57;)f=r.charCodeAt(++m);let g=t.slice(u,c),w=r.slice(d,m),v=Number(g)-Number(w);if(v)return v;if(g<w)return-1;if(g>w)return 1;continue}if(a!==f)return a-f}return t.length-r.length}var $n=/^\d+\/\d+$/;function Rr(t){let r=new B(n=>({name:n,utility:n,fraction:!1,modifiers:[]}));for(let n of t.utilities.keys("static")){let s=r.get(n);s.fraction=!1,s.modifiers=[]}for(let n of t.utilities.keys("functional")){let s=t.utilities.getCompletions(n);for(let a of s)for(let f of a.values){let u=f!==null&&$n.test(f),c=f===null?n:`${n}-${f}`,d=r.get(c);if(d.utility=n,d.fraction||=u,d.modifiers.push(...a.modifiers),a.supportsNegative){let m=r.get(`-${c}`);m.utility=`-${n}`,m.fraction||=u,m.modifiers.push(...a.modifiers)}}}if(r.size===0)return[];let i=Array.from(r.values());return i.sort((n,s)=>st(n.name,s.name)),Vn(i)}function Vn(t){let r=[],i=null,e=new Map,n=new B(()=>[]);for(let a of t){let{utility:f,fraction:u}=a;i||(i={utility:f,items:[]},e.set(f,i)),f!==i.utility&&(r.push(i),i={utility:f,items:[]},e.set(f,i)),u?n.get(f).push(a):i.items.push(a)}i&&r[r.length-1]!==i&&r.push(i);for(let[a,f]of n){let u=e.get(a);u&&u.items.push(...f)}let s=[];for(let a of r)for(let f of a.items)s.push([f.name,{modifiers:f.modifiers}]);return s}function Pr(t){let r=[];for(let[e,n]of t.variants.entries()){let f=function({value:u,modifier:c}={}){let d=e;u&&(d+=s?`-${u}`:u),c&&(d+=`/${c}`);let m=t.parseVariant(d);if(!m)return[];let g=M(".__placeholder__",[]);if(Ee(g,m,t.variants)===null)return[];let w=[];return Xe(g.nodes,(v,{path:x})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;x.sort((b,S)=>{let R=b.kind==="at-rule",L=S.kind==="at-rule";return R&&!L?-1:!R&&L?1:0});let y=x.flatMap(b=>b.kind==="rule"?b.selector==="&"?[]:[b.selector]:b.kind==="at-rule"?[`${b.name} ${b.params}`]:[]),V="";for(let b=y.length-1;b>=0;b--)V=V===""?y[b]:`${y[b]} { ${V} }`;w.push(V)}),w};var i=f;if(n.kind==="arbitrary")continue;let s=e!=="@",a=t.variants.getCompletions(e);switch(n.kind){case"static":{r.push({name:e,values:a,isArbitrary:!1,hasDash:s,selectors:f});break}case"functional":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:f});break}case"compound":{r.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:f});break}}}return r}function Or(t,r){let{astNodes:i,nodeSorting:e}=ge(Array.from(r),t),n=new Map(r.map(a=>[a,null])),s=0n;for(let a of i){let f=e.get(a)?.candidate;f&&n.set(f,n.get(f)??s++)}return r.map(a=>[a,n.get(a)??null])}var ut=/^@?[a-zA-Z0-9_-]*$/;var Kt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}fromAst(r,i){let e=[];I(i,n=>{n.kind==="rule"?e.push(n.selector):n.kind==="at-rule"&&n.name!=="@slot"&&e.push(`${n.name} ${n.params}`)}),this.static(r,n=>{let s=structuredClone(i);Ut(s,n.nodes),n.nodes=s},{compounds:Ae(e)})}functional(r,i,{compounds:e,order:n}={}){this.set(r,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}compound(r,i,e,{compounds:n,order:s}={}){this.set(r,{kind:"compound",applyFn:e,compoundsWith:i,compounds:n??2,order:s})}group(r,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),r(),this.groupOrder=null}has(r){return this.variants.has(r)}get(r){return this.variants.get(r)}kind(r){return this.variants.get(r)?.kind}compoundsWith(r,i){let e=this.variants.get(r),n=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:Ae([i.selector])}:this.variants.get(i.root);return!(!e||!n||e.kind!=="compound"||n.compounds===0||e.compoundsWith===0||(e.compoundsWith&n.compounds)===0)}suggest(r,i){this.completions.set(r,i)}getCompletions(r){return this.completions.get(r)?.()??[]}compare(r,i){if(r===i)return 0;if(r===null)return-1;if(i===null)return 1;if(r.kind==="arbitrary"&&i.kind==="arbitrary")return r.selector<i.selector?-1:1;if(r.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(r.root).order,n=this.variants.get(i.root).order,s=e-n;if(s!==0)return s;if(r.kind==="compound"&&i.kind==="compound"){let c=this.compare(r.variant,i.variant);return c!==0?c:r.modifier&&i.modifier?r.modifier.value<i.modifier.value?-1:1:r.modifier?1:i.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(r,i);if(r.root!==i.root)return r.root<i.root?-1:1;let f=r.value,u=i.value;return f===null?-1:u===null||f.kind==="arbitrary"&&u.kind!=="arbitrary"?1:f.kind!=="arbitrary"&&u.kind==="arbitrary"||f.value<u.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(r,{kind:i,applyFn:e,compounds:n,compoundsWith:s,order:a}){let f=this.variants.get(r);f?Object.assign(f,{kind:i,applyFn:e,compounds:n}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(r,{kind:i,applyFn:e,order:a,compoundsWith:s,compounds:n}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function Ae(t){let r=0;for(let i of t){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;r|=1;continue}if(i.includes("::"))return 0;r|=2}return r}function Dr(t){let r=new Kt;function i(c,d,{compounds:m}={}){m=m??Ae(d),r.static(c,g=>{g.nodes=d.map(w=>G(w,g.nodes))},{compounds:m})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(c,d){return d.map(m=>{m=m.trim();let g=D(m," ");return g[0]==="not"?g.slice(1).join(" "):c==="@container"?g[0][0]==="("?`not ${m}`:g[1]==="not"?`${g[0]} ${g.slice(2).join(" ")}`:`${g[0]} not ${g.slice(1).join(" ")}`:`not ${m}`})}let n=["@media","@supports","@container"];function s(c){for(let d of n){if(d!==c.name)continue;let m=D(c.params,",");return m.length>1?null:(m=e(c.name,m),F(c.name,m.join(", ")))}return null}function a(c){return c.includes("::")?null:`&:not(${D(c,",").map(m=>(m=m.replaceAll("&","*"),m)).join(", ")})`}r.compound("not",3,(c,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative||d.modifier)return null;let m=!1;if(I([c],(g,{path:w})=>{if(g.kind!=="rule"&&g.kind!=="at-rule")return 0;if(g.nodes.length>0)return 0;let v=[],x=[];for(let V of w)V.kind==="at-rule"?v.push(V):V.kind==="rule"&&x.push(V);if(v.length>1)return 2;if(x.length>1)return 2;let y=[];for(let V of x){let b=a(V.selector);if(!b)return m=!1,2;y.push(M(b,[]))}for(let V of v){let b=s(V);if(!b)return m=!1,2;y.push(b)}return Object.assign(c,M("&",y)),m=!0,1}),c.kind==="rule"&&c.selector==="&"&&c.nodes.length===1&&Object.assign(c,c.nodes[0]),!m)return null}),r.suggest("not",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("not",c))),r.compound("group",2,(c,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let m=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}group\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}group)`,g=!1;if(I([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let y of v.slice(0,-1))if(y.kind==="rule")return g=!1,2;let x=w.selector.replaceAll("&",m);D(x,",").length>1&&(x=`:is(${x})`),w.selector=`&:is(${x} *)`,g=!0}),!g)return null}),r.suggest("group",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("group",c))),r.compound("peer",2,(c,d)=>{if(d.variant.kind==="arbitrary"&&d.variant.relative)return null;let m=d.modifier?`:where(.${t.prefix?`${t.prefix}\\:`:""}peer\\/${d.modifier.value})`:`:where(.${t.prefix?`${t.prefix}\\:`:""}peer)`,g=!1;if(I([c],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let y of v.slice(0,-1))if(y.kind==="rule")return g=!1,2;let x=w.selector.replaceAll("&",m);D(x,",").length>1&&(x=`:is(${x})`),w.selector=`&:is(${x} ~ *)`,g=!0}),!g)return null}),r.suggest("peer",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("peer",c))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let c=function(){return z([F("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var f=c;r.static("before",d=>{d.nodes=[M("&::before",[c(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0}),r.static("after",d=>{d.nodes=[M("&::after",[c(),l("content","var(--tw-content)"),...d.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),r.static("hover",c=>{c.nodes=[M("&:hover",[F("@media","(hover: hover)",c.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),r.compound("in",2,(c,d)=>{if(d.modifier)return null;let m=!1;if(I([c],(g,{path:w})=>{if(g.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;g.selector=`:where(${g.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),r.suggest("in",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("in",c))),r.compound("has",2,(c,d)=>{if(d.modifier)return null;let m=!1;if(I([c],(g,{path:w})=>{if(g.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;g.selector=`&:has(${g.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),r.suggest("has",()=>Array.from(r.keys()).filter(c=>r.compoundsWith("has",c))),r.functional("aria",(c,d)=>{if(!d.value||d.modifier)return null;d.value.kind==="arbitrary"?c.nodes=[M(`&[aria-${_r(d.value.value)}]`,c.nodes)]:c.nodes=[M(`&[aria-${d.value.value}="true"]`,c.nodes)]}),r.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),r.functional("data",(c,d)=>{if(!d.value||d.modifier)return null;c.nodes=[M(`&[data-${_r(d.value.value)}]`,c.nodes)]}),r.functional("nth",(c,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!T(d.value.value))return null;c.nodes=[M(`&:nth-child(${d.value.value})`,c.nodes)]}),r.functional("nth-last",(c,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!T(d.value.value))return null;c.nodes=[M(`&:nth-last-child(${d.value.value})`,c.nodes)]}),r.functional("nth-of-type",(c,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!T(d.value.value))return null;c.nodes=[M(`&:nth-of-type(${d.value.value})`,c.nodes)]}),r.functional("nth-last-of-type",(c,d)=>{if(!d.value||d.modifier||d.value.kind==="named"&&!T(d.value.value))return null;c.nodes=[M(`&:nth-last-of-type(${d.value.value})`,c.nodes)]}),r.functional("supports",(c,d)=>{if(!d.value||d.modifier)return null;let m=d.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let g=m.replace(/\b(and|or|not)\b/g," $1 ");c.nodes=[F("@supports",g,c.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),c.nodes=[F("@supports",m,c.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let c=function(d,m,g,w){if(d===m)return 0;let v=w.get(d);if(v===null)return g==="asc"?-1:1;let x=w.get(m);return x===null?g==="asc"?1:-1:ye(v,x,g)};var u=c;{let d=t.namespace("--breakpoint"),m=new B(g=>{switch(g.kind){case"static":return t.resolveValue(g.root,["--breakpoint"])??null;case"functional":{if(!g.value||g.modifier)return null;let w=null;return g.value.kind==="arbitrary"?w=g.value.value:g.value.kind==="named"&&(w=t.resolveValue(g.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("max",(g,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;g.nodes=[F("@media",`(width < ${v})`,g.nodes)]},{compounds:1})},(g,w)=>c(g,w,"desc",m)),r.suggest("max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{for(let[g,w]of t.namespace("--breakpoint"))g!==null&&r.static(g,v=>{v.nodes=[F("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});r.functional("min",(g,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;g.nodes=[F("@media",`(width >= ${v})`,g.nodes)]},{compounds:1})},(g,w)=>c(g,w,"asc",m)),r.suggest("min",()=>Array.from(d.keys()).filter(g=>g!==null))}{let d=t.namespace("--container"),m=new B(g=>{switch(g.kind){case"functional":{if(g.value===null)return null;let w=null;return g.value.kind==="arbitrary"?w=g.value.value:g.value.kind==="named"&&(w=t.resolveValue(g.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});r.group(()=>{r.functional("@max",(g,w)=>{let v=m.get(w);if(v===null)return null;g.nodes=[F("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,g.nodes)]},{compounds:1})},(g,w)=>c(g,w,"desc",m)),r.suggest("@max",()=>Array.from(d.keys()).filter(g=>g!==null)),r.group(()=>{r.functional("@",(g,w)=>{let v=m.get(w);if(v===null)return null;g.nodes=[F("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,g.nodes)]},{compounds:1}),r.functional("@min",(g,w)=>{let v=m.get(w);if(v===null)return null;g.nodes=[F("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,g.nodes)]},{compounds:1})},(g,w)=>c(g,w,"asc",m)),r.suggest("@min",()=>Array.from(d.keys()).filter(g=>g!==null)),r.suggest("@",()=>Array.from(d.keys()).filter(g=>g!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),r}function _r(t){if(t.includes("=")){let[r,...i]=D(t,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return t;if(e.length>1){let n=e[e.length-1];if(e[e.length-2]===" "&&(n==="i"||n==="I"||n==="s"||n==="S"))return`${r}="${e.slice(0,-2)}" ${n}`}return`${r}="${e}"`}return t}function Ut(t,r){I(t,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(r);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,z([F(i.name,i.params,i.nodes)])),1})}function Kr(t){let r=Vr(t),i=Dr(t),e=new B(u=>gr(u,f)),n=new B(u=>Array.from(mr(u,f))),s=new B(u=>{let c=Ur(u,f);try{Ve(c.map(({node:d})=>d),f)}catch{return[]}return c}),a=new B(u=>{for(let c of Qe(u))t.markUsedVariable(c)}),f={theme:t,utilities:r,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(u){let c=[];for(let d of u){let m=!1,{astNodes:g}=ge([d],this,{onInvalidCandidate(){m=!0}});g=be(g,f,0),g.length===0||m?c.push(null):c.push(oe(g))}return c},getClassOrder(u){return Or(this,u)},getClassList(){return Rr(this)},getVariants(){return Pr(this)},parseCandidate(u){return n.get(u)},parseVariant(u){return e.get(u)},compileAstNodes(u){return s.get(u)},printCandidate(u){return vr(f,u)},printVariant(u){return it(u)},getVariantOrder(){let u=Array.from(e.values());u.sort((g,w)=>this.variants.compare(g,w));let c=new Map,d,m=0;for(let g of u)g!==null&&(d!==void 0&&this.variants.compare(d,g)!==0&&m++,c.set(g,m),d=g);return c},resolveThemeValue(u,c=!0){let d=u.lastIndexOf("/"),m=null;d!==-1&&(m=u.slice(d+1).trim(),u=u.slice(0,d).trim());let g=t.resolve(null,[u],c?1:0)??void 0;return m&&g?Q(g,m):g},trackUsedVariables(u){a.get(u)}};return f}var Lt=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function ge(t,r,{onInvalidCandidate:i}={}){let e=new Map,n=[],s=new Map;for(let f of t){if(r.invalidCandidates.has(f)){i?.(f);continue}let u=r.parseCandidate(f);if(u.length===0){i?.(f);continue}s.set(f,u)}let a=r.getVariantOrder();for(let[f,u]of s){let c=!1;for(let d of u){let m=r.compileAstNodes(d);if(m.length!==0){c=!0;for(let{node:g,propertySort:w}of m){let v=0n;for(let x of d.variants)v|=1n<<BigInt(a.get(x));e.set(g,{properties:w,variants:v,candidate:f}),n.push(g)}}}c||i?.(f)}return n.sort((f,u)=>{let c=e.get(f),d=e.get(u);if(c.variants-d.variants!==0n)return Number(c.variants-d.variants);let m=0;for(;m<c.properties.order.length&&m<d.properties.order.length&&c.properties.order[m]===d.properties.order[m];)m+=1;return(c.properties.order[m]??1/0)-(d.properties.order[m]??1/0)||d.properties.count-c.properties.count||st(c.candidate,d.candidate)}),{astNodes:n,nodeSorting:e}}function Ur(t,r){let i=Sn(t,r);if(i.length===0)return[];let e=[],n=`.${de(t.raw)}`;for(let s of i){let a=En(s);(t.important||r.important)&&jr(s);let f={kind:"rule",selector:n,nodes:s};for(let u of t.variants)if(Ee(f,u,r.variants)===null)return[];e.push({node:f,propertySort:a})}return e}function Ee(t,r,i,e=0){if(r.kind==="arbitrary"){if(r.relative&&e===0)return null;t.nodes=[G(r.selector,t.nodes)];return}let{applyFn:n}=i.get(r.root);if(r.kind==="compound"){let a=F("@slot");if(Ee(a,r.variant,i,e+1)===null||r.root==="not"&&a.nodes.length>1)return null;for(let u of a.nodes)if(u.kind!=="rule"&&u.kind!=="at-rule"||n(u,r)===null)return null;I(a.nodes,u=>{if((u.kind==="rule"||u.kind==="at-rule")&&u.nodes.length<=0)return u.nodes=t.nodes,1}),t.nodes=a.nodes;return}if(n(t,r)===null)return null}function Lr(t){let r=t.options?.types??[];return r.length>1&&r.includes("any")}function Sn(t,r){if(t.kind==="arbitrary"){let a=t.value;return t.modifier&&(a=X(a,t.modifier,r.theme)),a===null?[]:[[l(t.property,a)]]}let i=r.utilities.get(t.root)??[],e=[],n=i.filter(a=>!Lr(a));for(let a of n){if(a.kind!==t.kind)continue;let f=a.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}if(e.length>0)return e;let s=i.filter(a=>Lr(a));for(let a of s){if(a.kind!==t.kind)continue;let f=a.compileFn(t);if(f!==void 0){if(f===null)return e;e.push(f)}}return e}function jr(t){for(let r of t)r.kind!=="at-root"&&(r.kind==="declaration"?r.important=!0:(r.kind==="rule"||r.kind==="at-rule")&&jr(r.nodes))}function En(t){let r=new Set,i=0,e=t.slice(),n=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,n))continue;if(s.property==="--tw-sort"){let f=Lt.indexOf(s.value??"");if(f!==-1){r.add(f),n=!0;continue}}let a=Lt.indexOf(s.property);a!==-1&&r.add(a)}else if(s.kind==="rule"||s.kind==="at-rule")for(let a of s.nodes)e.push(a)}return{order:Array.from(r).sort((s,a)=>s-a),count:i}}function je(t,r){let i=0,e=G("&",t),n=new Set,s=new B(()=>new Set),a=new B(()=>new Set);I([e],(m,{parent:g,path:w})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return I(m.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let v=m.params.replace(/-\*$/,"");a.get(v).add(m),I(m.nodes,x=>{if(!(x.kind!=="at-rule"||x.name!=="@apply")){n.add(m);for(let y of Ir(x,r))s.get(m).add(y)}});return}if(m.name==="@apply"){if(g===null)return;i|=1,n.add(g);for(let v of Ir(m,r))for(let x of w)x!==m&&n.has(x)&&s.get(x).add(v)}}});let f=new Set,u=[],c=new Set;function d(m,g=[]){if(!f.has(m)){if(c.has(m)){let w=g[(g.indexOf(m)+1)%g.length];throw m.kind==="at-rule"&&m.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&I(m.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let x=v.params.split(/\s+/g);for(let y of x)for(let V of r.parseCandidate(y))switch(V.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===V.root)throw new Error(`You cannot \`@apply\` the \`${y}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${oe([m])}
Relies on:

${oe([w])}`)}c.add(m);for(let w of s.get(m))for(let v of a.get(w))g.push(m),d(v,g),g.pop();f.add(m),c.delete(m),u.push(m)}}for(let m of n)d(m);for(let m of u)"nodes"in m&&I(m.nodes,(g,{replaceWith:w})=>{if(g.kind!=="at-rule"||g.name!=="@apply")return;let v=g.params.split(/(\s+)/g),x={},y=0;for(let[V,b]of v.entries())V%2===0&&(x[b]=y),y+=b.length;{let V=Object.keys(x),b=ge(V,r,{onInvalidCandidate:P=>{if(r.theme.prefix&&!P.startsWith(r.theme.prefix))throw new Error(`Cannot apply unprefixed utility class \`${P}\`. Did you mean \`${r.theme.prefix}:${P}\`?`);if(r.invalidCandidates.has(P))throw new Error(`Cannot apply utility class \`${P}\` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes`);let K=D(P,":");if(K.length>1){let _=K.pop();if(r.candidatesToCss([_])[0]){let H=r.candidatesToCss(K.map(W=>`${W}:[--tw-variant-check:1]`)),j=K.filter((W,J)=>H[J]===null);if(j.length>0){if(j.length===1)throw new Error(`Cannot apply utility class \`${P}\` because the ${j.map(W=>`\`${W}\``)} variant does not exist.`);{let W=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw new Error(`Cannot apply utility class \`${P}\` because the ${W.format(j.map(J=>`\`${J}\``))} variants do not exist.`)}}}}throw r.theme.size===0?new Error(`Cannot apply unknown utility class \`${P}\`. Are you using CSS modules or similar and missing \`@reference\`? https://tailwindcss.com/docs/functions-and-directives#reference-directive`):new Error(`Cannot apply unknown utility class \`${P}\``)}}),S=g.src,R=b.astNodes.map(P=>{let K=b.nodeSorting.get(P)?.candidate,_=K?x[K]:void 0;if(P=structuredClone(P),!S||!K||_===void 0)return I([P],j=>{j.src=S}),P;let H=[S[0],S[1],S[2]];return H[1]+=7+_,H[2]=H[1]+K.length,I([P],j=>{j.src=H}),P}),L=[];for(let P of R)if(P.kind==="rule")for(let K of P.nodes)L.push(K);else L.push(P);w(L)}});return i}function*Ir(t,r){for(let i of t.params.split(/\s+/g))for(let e of r.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function jt(t,r,i,e=0,n=!1){let s=0,a=[];return I(t,(f,{replaceWith:u})=>{if(f.kind==="at-rule"&&(f.name==="@import"||f.name==="@reference")){let c=Tn(q(f.params));if(c===null)return;f.name==="@reference"&&(c.media="reference"),s|=2;let{uri:d,layer:m,media:g,supports:w}=c;if(d.startsWith("data:")||d.startsWith("http://")||d.startsWith("https://"))return;let v=se({},[]);return a.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${d}\` in \`${r}\`)`);let x=await i(d,r),y=ve(x.content,{from:n?x.path:void 0});await jt(y,x.base,i,e+1,n),v.nodes=Rn(f,[se({base:x.base},y)],m,g,w)})()),u(v),1}}),a.length>0&&await Promise.all(a),s}function Tn(t){let r,i=null,e=null,n=null;for(let s=0;s<t.length;s++){let a=t[s];if(a.kind!=="separator"){if(a.kind==="word"&&!r){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;r=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!r)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(i)return null;if(n)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?i=Z(a.nodes):i="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(n)return null;n=Z(a.nodes);continue}e=Z(t.slice(s));break}}return r?{uri:r,layer:i,media:e,supports:n}:null}function Rn(t,r,i,e,n){let s=r;if(i!==null){let a=F("@layer",i,s);a.src=t.src,s=[a]}if(e!==null){let a=F("@media",e,s);a.src=t.src,s=[a]}if(n!==null){let a=F("@supports",n[0]==="("?n:`(${n})`,s);a.src=t.src,s=[a]}return s}function Te(t,r=null){return Array.isArray(t)&&t.length===2&&typeof t[1]=="object"&&typeof t[1]!==null?r?t[1][r]??null:t[0]:Array.isArray(t)&&r===null?t.join(", "):typeof t=="string"&&r===null?t:null}function zr(t,{theme:r},i){for(let e of i){let n=ct([e]);n&&t.theme.clearNamespace(`--${n}`,4)}for(let[e,n]of Pn(r)){if(typeof n!="string"&&typeof n!="number")continue;if(typeof n=="string"&&(n=n.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof n=="number"||typeof n=="string")){let a=typeof n=="string"?parseFloat(n):n;a>=0&&a<=1&&(n=a*100+"%")}let s=ct(e);s&&t.theme.add(`--${s}`,""+n,7)}if(Object.hasOwn(r,"fontFamily")){let e=5;{let n=Te(r.fontFamily.sans);n&&t.theme.hasDefault("--font-sans")&&(t.theme.add("--default-font-family",n,e),t.theme.add("--default-font-feature-settings",Te(r.fontFamily.sans,"fontFeatureSettings")??"normal",e),t.theme.add("--default-font-variation-settings",Te(r.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let n=Te(r.fontFamily.mono);n&&t.theme.hasDefault("--font-mono")&&(t.theme.add("--default-mono-font-family",n,e),t.theme.add("--default-mono-font-feature-settings",Te(r.fontFamily.mono,"fontFeatureSettings")??"normal",e),t.theme.add("--default-mono-font-variation-settings",Te(r.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return r}function Pn(t){let r=[];return Fr(t,[],(i,e)=>{if(_n(i))return r.push([e,i]),1;if(Dn(i)){r.push([e,i[0]]);for(let n of Reflect.ownKeys(i[1]))r.push([[...e,`-${n}`],i[1][n]]);return 1}if(Array.isArray(i)&&i.every(n=>typeof n=="string"))return e[0]==="fontSize"?(r.push([e,i[0]]),i.length>=2&&r.push([[...e,"-line-height"],i[1]])):r.push([e,i.join(", ")]),1}),r}var On=/^[a-zA-Z0-9-_%/\.]+$/;function ct(t){if(t[0]==="container")return null;t=structuredClone(t),t[0]==="animation"&&(t[0]="animate"),t[0]==="aspectRatio"&&(t[0]="aspect"),t[0]==="borderRadius"&&(t[0]="radius"),t[0]==="boxShadow"&&(t[0]="shadow"),t[0]==="colors"&&(t[0]="color"),t[0]==="containers"&&(t[0]="container"),t[0]==="fontFamily"&&(t[0]="font"),t[0]==="fontSize"&&(t[0]="text"),t[0]==="letterSpacing"&&(t[0]="tracking"),t[0]==="lineHeight"&&(t[0]="leading"),t[0]==="maxWidth"&&(t[0]="container"),t[0]==="screens"&&(t[0]="breakpoint"),t[0]==="transitionTimingFunction"&&(t[0]="ease");for(let r of t)if(!On.test(r))return null;return t.map((r,i,e)=>r==="1"&&i!==e.length-1?"":r).map(r=>r.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,n)=>`${e}-${n.toLowerCase()}`)).filter((r,i)=>r!=="DEFAULT"||i!==t.length-1).join("-")}function _n(t){return typeof t=="number"||typeof t=="string"}function Dn(t){if(!Array.isArray(t)||t.length!==2||typeof t[0]!="string"&&typeof t[0]!="number"||t[1]===void 0||t[1]===null||typeof t[1]!="object")return!1;for(let r of Reflect.ownKeys(t[1]))if(typeof r!="string"||typeof t[1][r]!="string"&&typeof t[1][r]!="number")return!1;return!0}function Fr(t,r=[],i){for(let e of Reflect.ownKeys(t)){let n=t[e];if(n==null)continue;let s=[...r,e],a=i(n,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(n)&&typeof n!="object")&&Fr(n,s,i)===2)return 2}}}function ft(t){let r=[];for(let i of D(t,".")){if(!i.includes("[")){r.push(i);continue}let e=0;for(;;){let n=i.indexOf("[",e),s=i.indexOf("]",n);if(n===-1||s===-1)break;n>e&&r.push(i.slice(e,n)),r.push(i.slice(n+1,s)),e=s+1}e<=i.length-1&&r.push(i.slice(e))}return r}function Re(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let r=Object.getPrototypeOf(t);return r===null||Object.getPrototypeOf(r)===null}function Ie(t,r,i,e=[]){for(let n of r)if(n!=null)for(let s of Reflect.ownKeys(n)){e.push(s);let a=i(t[s],n[s],e);a!==void 0?t[s]=a:!Re(t[s])||!Re(n[s])?t[s]=n[s]:t[s]=Ie({},[t[s],n[s]],i,e),e.pop()}return t}function pt(t,r,i){return function(n,s){let a=n.lastIndexOf("/"),f=null;a!==-1&&(f=n.slice(a+1).trim(),n=n.slice(0,a).trim());let u=(()=>{let c=ft(n),[d,m]=Kn(t.theme,c),g=i(Mr(r()??{},c)??null);if(typeof g=="string"&&(g=g.replace("<alpha-value>","1")),typeof d!="object")return typeof m!="object"&&m&4?g??d:d;if(g!==null&&typeof g=="object"&&!Array.isArray(g)){let w=Ie({},[g],(v,x)=>x);if(d===null&&Object.hasOwn(g,"__CSS_VALUES__")){let v={};for(let x in g.__CSS_VALUES__)v[x]=g[x],delete w[x];d=v}for(let v in d)v!=="__CSS_VALUES__"&&(g?.__CSS_VALUES__?.[v]&4&&Mr(w,v.split("-"))!==void 0||(w[we(v)]=d[v]));return w}if(Array.isArray(d)&&Array.isArray(m)&&Array.isArray(g)){let w=d[0],v=d[1];m[0]&4&&(w=g[0]??w);for(let x of Object.keys(v))m[1][x]&4&&(v[x]=g[1][x]??v[x]);return[w,v]}return d??g})();return f&&typeof u=="string"&&(u=Q(u,f)),u??s}}function Kn(t,r){if(r.length===1&&r[0].startsWith("--"))return[t.get([r[0]]),t.getOptions(r[0])];let i=ct(r),e=new Map,n=new B(()=>new Map),s=t.namespace(`--${i}`);if(s.size===0)return[null,0];let a=new Map;for(let[d,m]of s){if(!d||!d.includes("--")){e.set(d,m),a.set(d,t.getOptions(d?`--${i}-${d}`:`--${i}`));continue}let g=d.indexOf("--"),w=d.slice(0,g),v=d.slice(g+2);v=v.replace(/-([a-z])/g,(x,y)=>y.toUpperCase()),n.get(w===""?null:w).set(v,[m,t.getOptions(`--${i}${d}`)])}let f=t.getOptions(`--${i}`);for(let[d,m]of n){let g=e.get(d);if(typeof g!="string")continue;let w={},v={};for(let[x,[y,V]]of m)w[x]=y,v[x]=V;e.set(d,[g,w]),a.set(d,[f,v])}let u={},c={};for(let[d,m]of e)Wr(u,[d??"DEFAULT"],m);for(let[d,m]of a)Wr(c,[d??"DEFAULT"],m);return r[r.length-1]==="DEFAULT"?[u?.DEFAULT??null,c.DEFAULT??0]:"DEFAULT"in u&&Object.keys(u).length===1?[u.DEFAULT,c.DEFAULT??0]:(u.__CSS_VALUES__=c,[u,c])}function Mr(t,r){for(let i=0;i<r.length;++i){let e=r[i];if(t?.[e]===void 0){if(r[i+1]===void 0)return;r[i+1]=`${e}-${r[i+1]}`;continue}t=t[e]}return t}function Wr(t,r,i){for(let e of r.slice(0,-1))t[e]===void 0&&(t[e]={}),t=t[e];t[r[r.length-1]]=i}function Un(t){return{kind:"combinator",value:t}}function Ln(t,r){return{kind:"function",value:t,nodes:r}}function ze(t){return{kind:"selector",value:t}}function jn(t){return{kind:"separator",value:t}}function In(t){return{kind:"value",value:t}}function Fe(t,r,i=null){for(let e=0;e<t.length;e++){let n=t[e],s=!1,a=0,f=r(n,{parent:i,replaceWith(u){s||(s=!0,Array.isArray(u)?u.length===0?(t.splice(e,1),a=0):u.length===1?(t[e]=u[0],a=1):(t.splice(e,1,...u),a=u.length):(t[e]=u,a=1))}})??0;if(s){f===0?e--:e+=a-1;continue}if(f===2)return 2;if(f!==1&&n.kind==="function"&&Fe(n.nodes,r,n)===2)return 2}}function Me(t){let r="";for(let i of t)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{r+=i.value;break}case"function":r+=i.value+"("+Me(i.nodes)+")"}return r}var Br=92,zn=93,qr=41,Fn=58,Hr=44,Mn=34,Wn=46,Gr=62,Yr=10,Bn=35,Zr=91,Jr=40,Qr=43,qn=39,Xr=32,ei=9,ti=126;function dt(t){t=t.replaceAll(`\r
`,`
`);let r=[],i=[],e=null,n="",s;for(let a=0;a<t.length;a++){let f=t.charCodeAt(a);switch(f){case Hr:case Gr:case Yr:case Xr:case Qr:case ei:case ti:{if(n.length>0){let g=ze(n);e?e.nodes.push(g):r.push(g),n=""}let u=a,c=a+1;for(;c<t.length&&(s=t.charCodeAt(c),!(s!==Hr&&s!==Gr&&s!==Yr&&s!==Xr&&s!==Qr&&s!==ei&&s!==ti));c++);a=c-1;let d=t.slice(u,c),m=d.trim()===","?jn(d):Un(d);e?e.nodes.push(m):r.push(m);break}case Jr:{let u=Ln(n,[]);if(n="",u.value!==":not"&&u.value!==":where"&&u.value!==":has"&&u.value!==":is"){let c=a+1,d=0;for(let g=a+1;g<t.length;g++){if(s=t.charCodeAt(g),s===Jr){d++;continue}if(s===qr){if(d===0){a=g;break}d--}}let m=a;u.nodes.push(In(t.slice(c,m))),n="",a=m,e?e.nodes.push(u):r.push(u);break}e?e.nodes.push(u):r.push(u),i.push(u),e=u;break}case qr:{let u=i.pop();if(n.length>0){let c=ze(n);u.nodes.push(c),n=""}i.length>0?e=i[i.length-1]:e=null;break}case Wn:case Fn:case Bn:{if(n.length>0){let u=ze(n);e?e.nodes.push(u):r.push(u)}n=String.fromCharCode(f);break}case Zr:{if(n.length>0){let d=ze(n);e?e.nodes.push(d):r.push(d)}n="";let u=a,c=0;for(let d=a+1;d<t.length;d++){if(s=t.charCodeAt(d),s===Zr){c++;continue}if(s===zn){if(c===0){a=d;break}c--}}n+=t.slice(u,a+1);break}case qn:case Mn:{let u=a;for(let c=a+1;c<t.length;c++)if(s=t.charCodeAt(c),s===Br)c+=1;else if(s===f){a=c;break}n+=t.slice(u,a+1);break}case Br:{let u=t.charCodeAt(a+1);n+=String.fromCharCode(f)+String.fromCharCode(u),a+=1;break}default:n+=String.fromCharCode(f)}}return n.length>0&&r.push(ze(n)),r}var ri=/^[a-z@][a-zA-Z0-9/%._-]*$/;function It({designSystem:t,ast:r,resolvedConfig:i,featuresRef:e,referenceMode:n}){let s={addBase(a){if(n)return;let f=ue(a);e.current|=Ve(f,t),r.push(F("@layer","base",f))},addVariant(a,f){if(!ut.test(a))throw new Error(`\`addVariant('${a}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(typeof f=="string"){if(f.includes(":merge("))return}else if(Array.isArray(f)){if(f.some(c=>c.includes(":merge(")))return}else if(typeof f=="object"){let c=function(d,m){return Object.entries(d).some(([g,w])=>g.includes(m)||typeof w=="object"&&c(w,m))};var u=c;if(c(f,":merge("))return}typeof f=="string"||Array.isArray(f)?t.variants.static(a,c=>{c.nodes=ii(f,c.nodes)},{compounds:Ae(typeof f=="string"?[f]:f)}):typeof f=="object"&&t.variants.fromAst(a,ue(f))},matchVariant(a,f,u){function c(m,g,w){let v=f(m,{modifier:g?.value??null});return ii(v,w)}try{let m=f("a",{modifier:null});if(typeof m=="string"&&m.includes(":merge("))return;if(Array.isArray(m)&&m.some(g=>g.includes(":merge(")))return}catch{}let d=Object.keys(u?.values??{});t.variants.group(()=>{t.variants.functional(a,(m,g)=>{if(!g.value){if(u?.values&&"DEFAULT"in u.values){m.nodes=c(u.values.DEFAULT,g.modifier,m.nodes);return}return null}if(g.value.kind==="arbitrary")m.nodes=c(g.value.value,g.modifier,m.nodes);else if(g.value.kind==="named"&&u?.values){let w=u.values[g.value.value];if(typeof w!="string")return;m.nodes=c(w,g.modifier,m.nodes)}})},(m,g)=>{if(m.kind!=="functional"||g.kind!=="functional")return 0;let w=m.value?m.value.value:"DEFAULT",v=g.value?g.value.value:"DEFAULT",x=u?.values?.[w]??w,y=u?.values?.[v]??v;if(u&&typeof u.sort=="function")return u.sort({value:x,modifier:m.modifier?.value??null},{value:y,modifier:g.modifier?.value??null});let V=d.indexOf(w),b=d.indexOf(v);return V=V===-1?d.length:V,b=b===-1?d.length:b,V!==b?V-b:x<y?-1:1})},addUtilities(a){a=Array.isArray(a)?a:[a];let f=a.flatMap(c=>Object.entries(c));f=f.flatMap(([c,d])=>D(c,",").map(m=>[m.trim(),d]));let u=new B(()=>[]);for(let[c,d]of f){if(c.startsWith("@keyframes ")){n||r.push(G(c,ue(d)));continue}let m=dt(c),g=!1;if(Fe(m,w=>{if(w.kind==="selector"&&w.value[0]==="."&&ri.test(w.value.slice(1))){let v=w.value;w.value="&";let x=Me(m),y=v.slice(1),V=x==="&"?ue(d):[G(x,ue(d))];u.get(y).push(...V),g=!0,w.value=v;return}if(w.kind==="function"&&w.value===":not")return 1}),!g)throw new Error(`\`addUtilities({ '${c}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[c,d]of u)t.theme.prefix&&I(d,m=>{if(m.kind==="rule"){let g=dt(m.selector);Fe(g,w=>{w.kind==="selector"&&w.value[0]==="."&&(w.value=`.${t.theme.prefix}\\:${w.value.slice(1)}`)}),m.selector=Me(g)}}),t.utilities.static(c,m=>{let g=structuredClone(d);return ni(g,c,m.raw),e.current|=je(g,t),g})},matchUtilities(a,f){let u=f?.type?Array.isArray(f?.type)?f.type:[f.type]:["any"];for(let[d,m]of Object.entries(a)){let g=function({negative:w}){return v=>{if(v.value?.kind==="arbitrary"&&u.length>0&&!u.includes("any")&&(v.value.dataType&&!u.includes(v.value.dataType)||!v.value.dataType&&!Y(v.value.value,u)))return;let x=u.includes("color"),y=null,V=!1;{let R=f?.values??{};x&&(R=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},R)),v.value?v.value.kind==="arbitrary"?y=v.value.value:v.value.fraction&&R[v.value.fraction]?(y=R[v.value.fraction],V=!0):R[v.value.value]?y=R[v.value.value]:R.__BARE_VALUE__&&(y=R.__BARE_VALUE__(v.value)??null,V=(v.value.fraction!==null&&y?.includes("/"))??!1):y=R.DEFAULT??null}if(y===null)return;let b;{let R=f?.modifiers??null;v.modifier?R==="any"||v.modifier.kind==="arbitrary"?b=v.modifier.value:R?.[v.modifier.value]?b=R[v.modifier.value]:x&&!Number.isNaN(Number(v.modifier.value))?b=`${v.modifier.value}%`:b=null:b=null}if(v.modifier&&b===null&&!V)return v.value?.kind==="arbitrary"?null:void 0;x&&b!==null&&(y=Q(y,b)),w&&(y=`calc(${y} * -1)`);let S=ue(m(y,{modifier:b}));return ni(S,d,v.raw),e.current|=je(S,t),S}};var c=g;if(!ri.test(d))throw new Error(`\`matchUtilities({ '${d}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);f?.supportsNegativeValues&&t.utilities.functional(`-${d}`,g({negative:!0}),{types:u}),t.utilities.functional(d,g({negative:!1}),{types:u}),t.utilities.suggest(d,()=>{let w=f?.values??{},v=new Set(Object.keys(w));v.delete("__BARE_VALUE__"),v.has("DEFAULT")&&(v.delete("DEFAULT"),v.add(null));let x=f?.modifiers??{},y=x==="any"?[]:Object.keys(x);return[{supportsNegative:f?.supportsNegativeValues??!1,values:Array.from(v),modifiers:y}]})}},addComponents(a,f){this.addUtilities(a,f)},matchComponents(a,f){this.matchUtilities(a,f)},theme:pt(t,()=>i.theme??{},a=>a),prefix(a){return a},config(a,f){let u=i;if(!a)return u;let c=ft(a);for(let d=0;d<c.length;++d){let m=c[d];if(u[m]===void 0)return f;u=u[m]}return u??f}};return s.addComponents=s.addComponents.bind(s),s.matchComponents=s.matchComponents.bind(s),s}function ue(t){let r=[];t=Array.isArray(t)?t:[t];let i=t.flatMap(e=>Object.entries(e));for(let[e,n]of i)if(typeof n!="object"){if(!e.startsWith("--")){if(n==="@slot"){r.push(G(e,[F("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}r.push(l(e,String(n)))}else if(Array.isArray(n))for(let s of n)typeof s=="string"?r.push(l(e,s)):r.push(G(e,ue(s)));else n!==null&&r.push(G(e,ue(n)));return r}function ii(t,r){return(typeof t=="string"?[t]:t).flatMap(e=>{if(e.trim().endsWith("}")){let n=e.replace("}","{@slot}}"),s=ve(n);return Ut(s,r),s}else return G(e,r)})}function ni(t,r,i){I(t,e=>{if(e.kind==="rule"){let n=dt(e.selector);Fe(n,s=>{s.kind==="selector"&&s.value===`.${r}`&&(s.value=`.${de(i)}`)}),e.selector=Me(n)}})}function oi(t,r,i){for(let e of Gn(r))t.theme.addKeyframes(e)}function Gn(t){let r=[];if("keyframes"in t.theme)for(let[i,e]of Object.entries(t.theme.keyframes))r.push(F("@keyframes",i,ue(e)));return r}var mt={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function Ce(t){return{__BARE_VALUE__:t}}var le=Ce(t=>{if(T(t.value))return t.value}),re=Ce(t=>{if(T(t.value))return`${t.value}%`}),he=Ce(t=>{if(T(t.value))return`${t.value}px`}),li=Ce(t=>{if(T(t.value))return`${t.value}ms`}),gt=Ce(t=>{if(T(t.value))return`${t.value}deg`}),Yn=Ce(t=>{if(t.fraction===null)return;let[r,i]=D(t.fraction,"/");if(!(!T(r)||!T(i)))return t.fraction}),ai=Ce(t=>{if(T(Number(t.value)))return`repeat(${t.value}, minmax(0, 1fr))`}),si={accentColor:({theme:t})=>t("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Yn},backdropBlur:({theme:t})=>t("blur"),backdropBrightness:({theme:t})=>({...t("brightness"),...re}),backdropContrast:({theme:t})=>({...t("contrast"),...re}),backdropGrayscale:({theme:t})=>({...t("grayscale"),...re}),backdropHueRotate:({theme:t})=>({...t("hueRotate"),...gt}),backdropInvert:({theme:t})=>({...t("invert"),...re}),backdropOpacity:({theme:t})=>({...t("opacity"),...re}),backdropSaturate:({theme:t})=>({...t("saturate"),...re}),backdropSepia:({theme:t})=>({...t("sepia"),...re}),backgroundColor:({theme:t})=>t("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:t})=>t("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),borderOpacity:({theme:t})=>t("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:t})=>t("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...he},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:t})=>t("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...re},caretColor:({theme:t})=>t("colors"),colors:()=>({...mt}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...le},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...re},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:t})=>t("borderColor"),divideOpacity:({theme:t})=>t("borderOpacity"),divideWidth:({theme:t})=>({...t("borderWidth"),...he}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:t})=>t("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...le},flexShrink:{0:"0",DEFAULT:"1",...le},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:t})=>t("spacing"),gradientColorStops:({theme:t})=>t("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...re},grayscale:{0:"0",DEFAULT:"100%",...re},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...le},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...ai},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...ai},height:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...gt},inset:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),invert:{0:"0",DEFAULT:"100%",...re},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:t})=>({auto:"auto",...t("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...le},maxHeight:({theme:t})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),maxWidth:({theme:t})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}),minHeight:({theme:t})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),minWidth:({theme:t})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...re},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...le},outlineColor:({theme:t})=>t("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},padding:({theme:t})=>t("spacing"),placeholderColor:({theme:t})=>t("colors"),placeholderOpacity:({theme:t})=>t("opacity"),ringColor:({theme:t})=>({DEFAULT:"currentcolor",...t("colors")}),ringOffsetColor:({theme:t})=>t("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},ringOpacity:({theme:t})=>({DEFAULT:"0.5",...t("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...gt},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...re},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...re},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:t})=>t("spacing"),scrollPadding:({theme:t})=>t("spacing"),sepia:{0:"0",DEFAULT:"100%",...re},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...gt},space:({theme:t})=>t("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:t})=>({none:"none",...t("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...le},supports:{},data:{},textColor:({theme:t})=>t("colors"),textDecorationColor:({theme:t})=>t("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},textIndent:({theme:t})=>t("spacing"),textOpacity:({theme:t})=>t("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...he},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...li},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...li},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:t})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}),size:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),width:({theme:t})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...le}};function ui(t){return{theme:{...si,colors:({theme:r})=>r("color",{}),extend:{fontSize:({theme:r})=>({...r("text",{})}),boxShadow:({theme:r})=>({...r("shadow",{})}),animation:({theme:r})=>({...r("animate",{})}),aspectRatio:({theme:r})=>({...r("aspect",{})}),borderRadius:({theme:r})=>({...r("radius",{})}),screens:({theme:r})=>({...r("breakpoint",{})}),letterSpacing:({theme:r})=>({...r("tracking",{})}),lineHeight:({theme:r})=>({...r("leading",{})}),transitionDuration:{DEFAULT:t.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:t.get(["--default-transition-timing-function"])??null},maxWidth:({theme:r})=>({...r("container",{})})}}}}var Zn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function Ft(t,r){let i={design:t,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(Zn)};for(let n of r)zt(i,n);for(let n of i.configs)"darkMode"in n&&n.darkMode!==void 0&&(i.result.darkMode=n.darkMode??null),"prefix"in n&&n.prefix!==void 0&&(i.result.prefix=n.prefix??""),"blocklist"in n&&n.blocklist!==void 0&&(i.result.blocklist=n.blocklist??[]),"important"in n&&n.important!==void 0&&(i.result.important=n.important??!1);let e=Qn(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function Jn(t,r){if(Array.isArray(t)&&Re(t[0]))return t.concat(r);if(Array.isArray(r)&&Re(r[0])&&Re(t))return[t,...r];if(Array.isArray(r))return r}function zt(t,{config:r,base:i,path:e,reference:n}){let s=[];for(let u of r.plugins??[])"__isOptionsFunction"in u?s.push({...u(),reference:n}):"handler"in u?s.push({...u,reference:n}):s.push({handler:u,reference:n});if(Array.isArray(r.presets)&&r.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let u of r.presets??[])zt(t,{path:e,base:i,config:u,reference:n});for(let u of s)t.plugins.push(u),u.config&&zt(t,{path:e,base:i,config:u.config,reference:!!u.reference});let a=r.content??[],f=Array.isArray(a)?a:a.files;for(let u of f)t.content.files.push(typeof u=="object"?u:{base:i,pattern:u});t.configs.push(r)}function Qn(t){let r=new Set,i=pt(t.design,()=>t.theme,n),e=Object.assign(i,{theme:i,colors:mt});function n(s){return typeof s=="function"?s(e)??null:s??null}for(let s of t.configs){let a=s.theme??{},f=a.extend??{};for(let u in a)u!=="extend"&&r.add(u);Object.assign(t.theme,a);for(let u in f)t.extend[u]??=[],t.extend[u].push(f[u])}delete t.theme.extend;for(let s in t.extend){let a=[t.theme[s],...t.extend[s]];t.theme[s]=()=>{let f=a.map(n);return Ie({},f,Jn)}}for(let s in t.theme)t.theme[s]=n(t.theme[s]);if(t.theme.screens&&typeof t.theme.screens=="object")for(let s of Object.keys(t.theme.screens)){let a=t.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(t.theme.screens[s]=a.min))}return r}function ci(t,r){let i=t.theme.container||{};if(typeof i!="object"||i===null)return;let e=Xn(i,r);e.length!==0&&r.utilities.static("container",()=>structuredClone(e))}function Xn({center:t,padding:r,screens:i},e){let n=[],s=null;if(t&&n.push(l("margin-inline","auto")),(typeof r=="string"||typeof r=="object"&&r!==null&&"DEFAULT"in r)&&n.push(l("padding-inline",typeof r=="string"?r:r.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((f,u)=>ye(f[1],u[1],"asc")),a.length>0){let[f]=a[0];n.push(F("@media",`(width >= --theme(--breakpoint-${f}))`,[l("max-width","none")]))}for(let[f,u]of Object.entries(i)){if(typeof u=="object")if("min"in u)u=u.min;else continue;s.set(f,F("@media",`(width >= ${u})`,[l("max-width",u)]))}}if(typeof r=="object"&&r!==null){let a=Object.entries(r).filter(([f])=>f!=="DEFAULT").map(([f,u])=>[f,e.theme.resolveValue(f,["--breakpoint"]),u]).filter(Boolean);a.sort((f,u)=>ye(f[1],u[1],"asc"));for(let[f,,u]of a)if(s&&s.has(f))s.get(f).nodes.push(l("padding-inline",u));else{if(s)continue;n.push(F("@media",`(width >= theme(--breakpoint-${f}))`,[l("padding-inline",u)]))}}if(s)for(let[,a]of s)n.push(a);return n}function fi({addVariant:t,config:r}){let i=r("darkMode",null),[e,n=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(n)||typeof n=="function"?s=n:typeof n=="string"&&(s=[n]),Array.isArray(s))for(let a of s)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=s}e===null||(e==="selector"?t("dark",`&:where(${n}, ${n} *)`):e==="media"?t("dark","@media (prefers-color-scheme: dark)"):e==="variant"?t("dark",n):e==="class"&&t("dark",`&:is(${n} *)`))}function pi(t){for(let[r,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])t.utilities.static(`bg-gradient-to-${r}`,()=>[l("--tw-gradient-position",`to ${i} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);t.utilities.static("bg-left-top",()=>[l("background-position","left top")]),t.utilities.static("bg-right-top",()=>[l("background-position","right top")]),t.utilities.static("bg-left-bottom",()=>[l("background-position","left bottom")]),t.utilities.static("bg-right-bottom",()=>[l("background-position","right bottom")]),t.utilities.static("object-left-top",()=>[l("object-position","left top")]),t.utilities.static("object-right-top",()=>[l("object-position","right top")]),t.utilities.static("object-left-bottom",()=>[l("object-position","left bottom")]),t.utilities.static("object-right-bottom",()=>[l("object-position","right bottom")]),t.utilities.functional("max-w-screen",r=>{if(!r.value||r.value.kind==="arbitrary")return;let i=t.theme.resolve(r.value.value,["--breakpoint"]);if(i)return[l("max-width",i)]}),t.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),t.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),t.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),t.utilities.functional("flex-shrink",r=>{if(!r.modifier){if(!r.value)return[l("flex-shrink","1")];if(r.value.kind==="arbitrary")return[l("flex-shrink",r.value.value)];if(T(r.value.value))return[l("flex-shrink",r.value.value)]}}),t.utilities.functional("flex-grow",r=>{if(!r.modifier){if(!r.value)return[l("flex-grow","1")];if(r.value.kind==="arbitrary")return[l("flex-grow",r.value.value)];if(T(r.value.value))return[l("flex-grow",r.value.value)]}}),t.utilities.static("order-none",()=>[l("order","0")])}function di(t,r){let i=t.theme.screens||{},e=r.variants.get("min")?.order??0,n=[];for(let[a,f]of Object.entries(i)){let g=function(w){r.variants.static(a,v=>{v.nodes=[F("@media",m,v.nodes)]},{order:w})};var s=g;let u=r.variants.get(a),c=r.theme.resolveValue(a,["--breakpoint"]);if(u&&c&&!r.theme.hasDefault(`--breakpoint-${a}`))continue;let d=!0;typeof f=="string"&&(d=!1);let m=eo(f);d?n.push(g):g(e)}if(n.length!==0){for(let[,a]of r.variants.variants)a.order>e&&(a.order+=n.length);r.variants.compareFns=new Map(Array.from(r.variants.compareFns).map(([a,f])=>(a>e&&(a+=n.length),[a,f])));for(let[a,f]of n.entries())f(e+a+1)}}function eo(t){return(Array.isArray(t)?t:[t]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function mi(t,r){let i=t.theme.aria||{},e=t.theme.supports||{},n=t.theme.data||{};if(Object.keys(i).length>0){let s=r.variants.get("aria"),a=s?.applyFn,f=s?.compounds;r.variants.functional("aria",(u,c)=>{let d=c.value;return d&&d.kind==="named"&&d.value in i?a?.(u,{...c,value:{kind:"arbitrary",value:i[d.value]}}):a?.(u,c)},{compounds:f})}if(Object.keys(e).length>0){let s=r.variants.get("supports"),a=s?.applyFn,f=s?.compounds;r.variants.functional("supports",(u,c)=>{let d=c.value;return d&&d.kind==="named"&&d.value in e?a?.(u,{...c,value:{kind:"arbitrary",value:e[d.value]}}):a?.(u,c)},{compounds:f})}if(Object.keys(n).length>0){let s=r.variants.get("data"),a=s?.applyFn,f=s?.compounds;r.variants.functional("data",(u,c)=>{let d=c.value;return d&&d.kind==="named"&&d.value in n?a?.(u,{...c,value:{kind:"arbitrary",value:n[d.value]}}):a?.(u,c)},{compounds:f})}}var to=/^[a-z]+$/;async function hi({designSystem:t,base:r,ast:i,loadModule:e,sources:n}){let s=0,a=[],f=[];I(i,(m,{parent:g,replaceWith:w,context:v})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(g!==null)throw new Error("`@plugin` cannot be nested.");let x=m.params.slice(1,-1);if(x.length===0)throw new Error("`@plugin` must have a path.");let y={};for(let V of m.nodes??[]){if(V.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${oe([V])}

\`@plugin\` options must be a flat list of declarations.`);if(V.value===void 0)continue;let b=V.value,S=D(b,",").map(R=>{if(R=R.trim(),R==="null")return null;if(R==="true")return!0;if(R==="false")return!1;if(Number.isNaN(Number(R))){if(R[0]==='"'&&R[R.length-1]==='"'||R[0]==="'"&&R[R.length-1]==="'")return R.slice(1,-1);if(R[0]==="{"&&R[R.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${oe([V]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(R);return R});y[V.property]=S.length===1?S[0]:S}a.push([{id:x,base:v.base,reference:!!v.reference},Object.keys(y).length>0?y:null]),w([]),s|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(g!==null)throw new Error("`@config` cannot be nested.");f.push({id:m.params.slice(1,-1),base:v.base,reference:!!v.reference}),w([]),s|=4;return}}}),pi(t);let u=t.resolveThemeValue;if(t.resolveThemeValue=function(g,w){return g.startsWith("--")?u(g,w):(s|=gi({designSystem:t,base:r,ast:i,sources:n,configs:[],pluginDetails:[]}),t.resolveThemeValue(g,w))},!a.length&&!f.length)return 0;let[c,d]=await Promise.all([Promise.all(f.map(async({id:m,base:g,reference:w})=>{let v=await e(m,g,"config");return{path:m,base:v.base,config:v.module,reference:w}})),Promise.all(a.map(async([{id:m,base:g,reference:w},v])=>{let x=await e(m,g,"plugin");return{path:m,base:x.base,plugin:x.module,options:v,reference:w}}))]);return s|=gi({designSystem:t,base:r,ast:i,sources:n,configs:c,pluginDetails:d}),s}function gi({designSystem:t,base:r,ast:i,sources:e,configs:n,pluginDetails:s}){let a=0,u=[...s.map(y=>{if(!y.options)return{config:{plugins:[y.plugin]},base:y.base,reference:y.reference};if("__isOptionsFunction"in y.plugin)return{config:{plugins:[y.plugin(y.options)]},base:y.base,reference:y.reference};throw new Error(`The plugin "${y.path}" does not accept options`)}),...n],{resolvedConfig:c}=Ft(t,[{config:ui(t.theme),base:r,reference:!0},...u,{config:{plugins:[fi]},base:r,reference:!0}]),{resolvedConfig:d,replacedThemeKeys:m}=Ft(t,u),g=t.resolveThemeValue;t.resolveThemeValue=function(V,b){if(V[0]==="-"&&V[1]==="-")return g(V,b);let S=v.theme(V,void 0);if(Array.isArray(S)&&S.length===2)return S[0];if(Array.isArray(S))return S.join(", ");if(typeof S=="string")return S};let w={designSystem:t,ast:i,resolvedConfig:c,featuresRef:{set current(y){a|=y}}},v=It({...w,referenceMode:!1}),x;for(let{handler:y,reference:V}of c.plugins)V?(x||=It({...w,referenceMode:!0}),y(x)):y(v);if(zr(t,d,m),oi(t,d,m),mi(d,t),di(d,t),ci(d,t),!t.theme.prefix&&c.prefix){if(c.prefix.endsWith("-")&&(c.prefix=c.prefix.slice(0,-1),console.warn(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!to.test(c.prefix))throw new Error(`The prefix "${c.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);t.theme.prefix=c.prefix}if(!t.important&&c.important===!0&&(t.important=!0),typeof c.important=="string"){let y=c.important;I(i,(V,{replaceWith:b,parent:S})=>{if(V.kind==="at-rule"&&!(V.name!=="@tailwind"||V.params!=="utilities"))return S?.kind==="rule"&&S.selector===y?2:(b(M(y,[V])),2)})}for(let y of c.blocklist)t.invalidCandidates.add(y);for(let y of c.content.files){if("raw"in y)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(y,null,2)}

This feature is not currently supported.`);let V=!1;y.pattern[0]=="!"&&(V=!0,y.pattern=y.pattern.slice(1)),e.push({...y,negated:V})}return a}function vi(t){let r=[0];for(let n=0;n<t.length;n++)t.charCodeAt(n)===10&&r.push(n+1);function i(n){let s=0,a=r.length;for(;a>0;){let u=(a|0)>>1,c=s+u;r[c]<=n?(s=c+1,a=a-u-1):a=u}s-=1;let f=n-r[s];return{line:s+1,column:f}}function e({line:n,column:s}){n-=1,n=Math.min(Math.max(n,0),r.length-1);let a=r[n],f=r[n+1]??a;return Math.min(Math.max(a+s,0),f)}return{find:i,findOffset:e}}function wi({ast:t}){let r=new B(n=>vi(n.code)),i=new B(n=>({url:n.file,content:n.code,ignore:!1})),e={file:null,sources:[],mappings:[]};I(t,n=>{if(!n.src||!n.dst)return;let s=i.get(n.src[0]);if(!s.content)return;let a=r.get(n.src[0]),f=r.get(n.dst[0]),u=s.content.slice(n.src[1],n.src[2]),c=0;for(let g of u.split(`
`)){if(g.trim()!==""){let w=a.find(n.src[1]+c),v=f.find(n.dst[1]);e.mappings.push({name:null,originalPosition:{source:s,...w},generatedPosition:v})}c+=g.length,c+=1}let d=a.find(n.src[2]),m=f.find(n.dst[2]);e.mappings.push({name:null,originalPosition:{source:s,...d},generatedPosition:m})});for(let n of r.keys())e.sources.push(i.get(n));return e.mappings.sort((n,s)=>n.generatedPosition.line-s.generatedPosition.line||n.generatedPosition.column-s.generatedPosition.column||(n.originalPosition?.line??0)-(s.originalPosition?.line??0)||(n.originalPosition?.column??0)-(s.originalPosition?.column??0)),e}var ki=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function ht(t){let r=t.indexOf("{");if(r===-1)return[t];let i=[],e=t.slice(0,r),n=t.slice(r),s=0,a=n.lastIndexOf("}");for(let m=0;m<n.length;m++){let g=n[m];if(g==="{")s++;else if(g==="}"&&(s--,s===0)){a=m;break}}if(a===-1)throw new Error(`The pattern \`${t}\` is not balanced.`);let f=n.slice(1,a),u=n.slice(a+1),c;ro(f)?c=io(f):c=D(f,","),c=c.flatMap(m=>ht(m));let d=ht(u);for(let m of d)for(let g of c)i.push(e+g+m);return i}function ro(t){return ki.test(t)}function io(t){let r=t.match(ki);if(!r)return[t];let[,i,e,n]=r,s=n?parseInt(n,10):void 0,a=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let f=parseInt(i,10),u=parseInt(e,10);if(s===void 0&&(s=f<=u?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let c=f<u;c&&s<0&&(s=-s),!c&&s>0&&(s=-s);for(let d=f;c?d<=u:d>=u;d+=s)a.push(d.toString())}return a}var no=/^[a-z]+$/,tt=(n=>(n[n.None=0]="None",n[n.AtProperty=1]="AtProperty",n[n.ColorMix=2]="ColorMix",n[n.All=3]="All",n))(tt||{});function oo(){throw new Error("No `loadModule` function provided to `compile`")}function lo(){throw new Error("No `loadStylesheet` function provided to `compile`")}function ao(t){let r=0,i=null;for(let e of D(t," "))e==="reference"?r|=2:e==="inline"?r|=1:e==="default"?r|=4:e==="static"?r|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[r,i]}var Se=(f=>(f[f.None=0]="None",f[f.AtApply=1]="AtApply",f[f.AtImport=2]="AtImport",f[f.JsPluginCompat=4]="JsPluginCompat",f[f.ThemeFunction=8]="ThemeFunction",f[f.Utilities=16]="Utilities",f[f.Variants=32]="Variants",f))(Se||{});async function bi(t,{base:r="",from:i,loadModule:e=oo,loadStylesheet:n=lo}={}){let s=0;t=[se({base:r},t)],s|=await jt(t,r,n,0,i!==void 0);let a=null,f=new Je,u=[],c=[],d=null,m=null,g=[],w=[],v=[],x=[],y=null;I(t,(b,{parent:S,replaceWith:R,context:L})=>{if(b.kind==="at-rule"){if(b.name==="@tailwind"&&(b.params==="utilities"||b.params.startsWith("utilities"))){if(m!==null){R([]);return}if(L.reference){R([]);return}let P=D(b.params," ");for(let K of P)if(K.startsWith("source(")){let _=K.slice(7,-1);if(_==="none"){y=_;continue}if(_[0]==='"'&&_[_.length-1]!=='"'||_[0]==="'"&&_[_.length-1]!=="'"||_[0]!=="'"&&_[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");y={base:L.sourceBase??L.base,pattern:_.slice(1,-1)}}m=b,s|=16}if(b.name==="@utility"){if(S!==null)throw new Error("`@utility` cannot be nested.");if(b.nodes.length===0)throw new Error(`\`@utility ${b.params}\` is empty. Utilities should include at least one property.`);let P=Sr(b);if(P===null)throw new Error(`\`@utility ${b.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`);c.push(P)}if(b.name==="@source"){if(b.nodes.length>0)throw new Error("`@source` cannot have a body.");if(S!==null)throw new Error("`@source` cannot be nested.");let P=!1,K=!1,_=b.params;if(_[0]==="n"&&_.startsWith("not ")&&(P=!0,_=_.slice(4)),_[0]==="i"&&_.startsWith("inline(")&&(K=!0,_=_.slice(7,-1)),_[0]==='"'&&_[_.length-1]!=='"'||_[0]==="'"&&_[_.length-1]!=="'"||_[0]!=="'"&&_[0]!=='"')throw new Error("`@source` paths must be quoted.");let H=_.slice(1,-1);if(K){let j=P?x:v,W=D(H," ");for(let J of W)for(let ie of ht(J))j.push(ie)}else w.push({base:L.base,pattern:H,negated:P});R([]);return}if(b.name==="@variant"&&(S===null?b.nodes.length===0?b.name="@custom-variant":(I(b.nodes,P=>{if(P.kind==="at-rule"&&P.name==="@slot")return b.name="@custom-variant",2}),b.name==="@variant"&&g.push(b)):g.push(b)),b.name==="@custom-variant"){if(S!==null)throw new Error("`@custom-variant` cannot be nested.");R([]);let[P,K]=D(b.params," ");if(!ut.test(P))throw new Error(`\`@custom-variant ${P}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(b.nodes.length>0&&K)throw new Error(`\`@custom-variant ${P}\` cannot have both a selector and a body.`);if(b.nodes.length===0){if(!K)throw new Error(`\`@custom-variant ${P}\` has no selector or body.`);let _=D(K.slice(1,-1),",");if(_.length===0||_.some(W=>W.trim()===""))throw new Error(`\`@custom-variant ${P} (${_.join(",")})\` selector is invalid.`);let H=[],j=[];for(let W of _)W=W.trim(),W[0]==="@"?H.push(W):j.push(W);u.push(W=>{W.variants.static(P,J=>{let ie=[];j.length>0&&ie.push(M(j.join(", "),J.nodes));for(let o of H)ie.push(G(o,J.nodes));J.nodes=ie},{compounds:Ae([...j,...H])})});return}else{u.push(_=>{_.variants.fromAst(P,b.nodes)});return}}if(b.name==="@media"){let P=D(b.params," "),K=[];for(let _ of P)if(_.startsWith("source(")){let H=_.slice(7,-1);I(b.nodes,(j,{replaceWith:W})=>{if(j.kind==="at-rule"&&j.name==="@tailwind"&&j.params==="utilities")return j.params+=` source(${H})`,W([se({sourceBase:L.base},[j])]),2})}else if(_.startsWith("theme(")){let H=_.slice(6,-1),j=H.includes("reference");I(b.nodes,W=>{if(W.kind!=="at-rule"){if(j)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(W.name==="@theme")return W.params+=" "+H,1})}else if(_.startsWith("prefix(")){let H=_.slice(7,-1);I(b.nodes,j=>{if(j.kind==="at-rule"&&j.name==="@theme")return j.params+=` prefix(${H})`,1})}else _==="important"?a=!0:_==="reference"?b.nodes=[se({reference:!0},b.nodes)]:K.push(_);K.length>0?b.params=K.join(" "):P.length>0&&R(b.nodes)}if(b.name==="@theme"){let[P,K]=ao(b.params);if(L.reference&&(P|=2),K){if(!no.test(K))throw new Error(`The prefix "${K}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);f.prefix=K}return I(b.nodes,_=>{if(_.kind==="at-rule"&&_.name==="@keyframes")return f.addKeyframes(_),1;if(_.kind==="comment")return;if(_.kind==="declaration"&&_.property.startsWith("--")){f.add(we(_.property),_.value??"",P,_.src);return}let H=oe([F(b.name,b.params,[_])]).split(`
`).map((j,W,J)=>`${W===0||W>=J.length-2?" ":">"} ${j}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${H}`)}),d?R([]):(d=M(":root, :host",[]),d.src=b.src,R([d])),1}}});let V=Kr(f);if(a&&(V.important=a),x.length>0)for(let b of x)V.invalidCandidates.add(b);s|=await hi({designSystem:V,base:r,ast:t,loadModule:e,sources:w});for(let b of u)b(V);for(let b of c)b(V);if(d){let b=[];for(let[R,L]of V.theme.entries()){if(L.options&2)continue;let P=l(de(R),L.value);P.src=L.src,b.push(P)}let S=V.theme.getKeyframes();for(let R of S)t.push(se({theme:!0},[z([R])]));d.nodes=[se({theme:!0},b)]}if(g.length>0){for(let b of g){let S=M("&",b.nodes),R=b.params,L=V.parseVariant(R);if(L===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${R}`);if(Ee(S,L,V.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${R}`);Object.assign(b,S)}s|=32}if(s|=Ve(t,V),s|=je(t,V),m){let b=m;b.kind="context",b.context={}}return I(t,(b,{replaceWith:S})=>{if(b.kind==="at-rule")return b.name==="@utility"&&S([]),1}),{designSystem:V,ast:t,sources:w,root:y,utilitiesNode:m,features:s,inlineCandidates:v}}async function yi(t,r={}){let{designSystem:i,ast:e,sources:n,root:s,utilitiesNode:a,features:f,inlineCandidates:u}=await bi(t,r);e.unshift(Ze(`! tailwindcss v${Mt} | MIT License | https://tailwindcss.com `));function c(v){i.invalidCandidates.add(v)}let d=new Set,m=null,g=0,w=!1;for(let v of u)i.invalidCandidates.has(v)||(d.add(v),w=!0);return{sources:n,root:s,features:f,build(v){if(f===0)return t;if(!a)return m??=be(e,i,r.polyfills),m;let x=w,y=!1;w=!1;let V=d.size;for(let S of v)if(!i.invalidCandidates.has(S))if(S[0]==="-"&&S[1]==="-"){let R=i.theme.markUsedVariable(S);x||=R,y||=R}else d.add(S),x||=d.size!==V;if(!x)return m??=be(e,i,r.polyfills),m;let b=ge(d,i,{onInvalidCandidate:c}).astNodes;return r.from&&I(b,S=>{S.src??=a.src}),!y&&g===b.length?(m??=be(e,i,r.polyfills),m):(g=b.length,a.nodes=b,m=be(e,i,r.polyfills),m)}}}async function so(t,r={}){let i=ve(t,{from:r.from}),e=await yi(i,r),n=i,s=t;return{...e,build(a){let f=e.build(a);return f===n||(s=oe(f,!!r.from),n=f),s},buildSourceMap(){return wi({ast:n})}}}async function uo(t,r={}){return(await bi(ve(t),r)).designSystem}function We(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let t in vt)t!=="default"&&(We[t]=vt[t]);module.exports=We;
