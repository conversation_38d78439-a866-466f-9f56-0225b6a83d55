#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/cli/dist/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/cli/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/cli/dist/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/cli/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules/@tailwindcss/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/@tailwindcss+cli@4.1.11/node_modules:/Users/<USER>/MAC/projects/sz-airlines/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/index.mjs" "$@"
else
  exec node  "$basedir/../../dist/index.mjs" "$@"
fi
